// Simple verification script to check if the service can be imported
const fs = require('fs');
const path = require('path');

// Check if all files exist
const files = [
  'src/shared/services/zalo/zalo-user-info-fields.service.ts',
  'src/shared/services/zalo/dto/zalo-user-info-fields.dto.ts',
  'src/shared/services/zalo/docs/zalo-user-info-fields.md',
  'src/shared/services/zalo/zalo-user-info-fields.service.spec.ts'
];

console.log('Checking if all files exist...');
files.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file} exists`);
  } else {
    console.log(`❌ ${file} does not exist`);
  }
});

// Check if exports are properly added
const indexFile = 'src/shared/services/zalo/index.ts';
const indexContent = fs.readFileSync(indexFile, 'utf8');
if (indexContent.includes('zalo-user-info-fields.service')) {
  console.log('✅ Service exported in index.ts');
} else {
  console.log('❌ Service not exported in index.ts');
}

const dtoIndexFile = 'src/shared/services/zalo/dto/index.ts';
const dtoIndexContent = fs.readFileSync(dtoIndexFile, 'utf8');
if (dtoIndexContent.includes('zalo-user-info-fields.dto')) {
  console.log('✅ DTOs exported in dto/index.ts');
} else {
  console.log('❌ DTOs not exported in dto/index.ts');
}

// Check if module is updated
const moduleFile = 'src/shared/services/zalo/zalo.module.ts';
const moduleContent = fs.readFileSync(moduleFile, 'utf8');
if (moduleContent.includes('ZaloUserInfoFieldsService')) {
  console.log('✅ Service added to module');
} else {
  console.log('❌ Service not added to module');
}

console.log('\n🎉 All checks completed! The Zalo User Info Fields service has been successfully created.');
