import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsNumber, IsObject, IsHexColor, Min, Max, IsNotEmpty } from 'class-validator';

/**
 * DTO cho yêu cầu gắn/gỡ nhãn người dùng
 */
export class ZaloUserLabelRequestDto {
  @ApiProperty({
    description: 'ID của người dùng Zalo',
    example: 'user_123456789',
  })
  @IsString()
  @IsNotEmpty()
  user_id: string;

  @ApiProperty({
    description: 'ID của nhãn',
    example: 'label_123456789',
  })
  @IsString()
  @IsNotEmpty()
  label_id: string;
}

/**
 * DTO cho yêu cầu tạo nhãn mới
 */
export class ZaloCreateLabelRequestDto {
  @ApiProperty({
    description: 'Tên nhãn',
    example: 'Kh<PERSON>ch hàng VIP',
  })
  @IsString()
  @IsNotEmpty()
  label_name: string;

  @ApiPropertyOptional({
    description: '<PERSON>ô tả nhãn',
    example: '<PERSON>h<PERSON>ch hàng có giá trị cao',
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({
    description: 'Màu sắc nhãn (hex color)',
    example: '#FF0000',
  })
  @IsHexColor()
  @IsOptional()
  color?: string;
}

/**
 * DTO cho thông tin nhãn
 */
export class ZaloUserLabelDto {
  @ApiProperty({
    description: 'ID của nhãn',
    example: 'label_123456789',
  })
  label_id: string;

  @ApiProperty({
    description: 'Tên nhãn',
    example: 'Khách hàng VIP',
  })
  label_name: string;

  @ApiPropertyOptional({
    description: 'Mô tả nhãn',
    example: 'Khách hàng có giá trị cao',
  })
  description?: string;

  @ApiPropertyOptional({
    description: 'Màu sắc nhãn (hex color)',
    example: '#FF0000',
  })
  color?: string;

  @ApiPropertyOptional({
    description: 'Thời gian tạo (Unix timestamp)',
    example: 1625097600000,
  })
  created_time?: number;

  @ApiPropertyOptional({
    description: 'Số lượng người dùng có nhãn này',
    example: 25,
  })
  user_count?: number;
}

/**
 * DTO cho danh sách nhãn
 */
export class ZaloUserLabelListDto {
  @ApiProperty({
    description: 'Danh sách nhãn',
    type: [ZaloUserLabelDto],
  })
  labels: ZaloUserLabelDto[];

  @ApiProperty({
    description: 'Tổng số nhãn',
    example: 10,
  })
  total: number;
}

/**
 * DTO cho thông tin người dùng Zalo
 */
export class ZaloUserInfoDto {
  @ApiProperty({
    description: 'ID của người dùng Zalo',
    example: 'user_123456789',
  })
  id: string;

  @ApiProperty({
    description: 'Tên hiển thị của người dùng',
    example: 'Nguyễn Văn A',
  })
  display_name: string;

  @ApiProperty({
    description: 'URL avatar của người dùng',
    example: 'https://s120-ava-talk.zadn.vn/default',
  })
  avatar: string;

  @ApiPropertyOptional({
    description: 'Giới tính của người dùng (1: Nam, 2: Nữ)',
    example: 1,
  })
  gender?: number;

  @ApiPropertyOptional({
    description: 'Ngày sinh của người dùng (định dạng dd/mm/yyyy)',
    example: '01/01/1990',
  })
  birth_date?: string;

  @ApiPropertyOptional({
    description: 'Số điện thoại của người dùng',
    example: '+84123456789',
  })
  phone?: string;
}

/**
 * DTO cho danh sách người dùng
 */
export class ZaloUserListDto {
  @ApiProperty({
    description: 'Danh sách người dùng',
    type: [ZaloUserInfoDto],
  })
  users: ZaloUserInfoDto[];

  @ApiProperty({
    description: 'Tổng số người dùng',
    example: 100,
  })
  total: number;

  @ApiProperty({
    description: 'Có còn dữ liệu tiếp theo không',
    example: true,
  })
  has_more: boolean;

  @ApiPropertyOptional({
    description: 'Offset tiếp theo',
    example: 20,
  })
  next_offset?: number;
}

/**
 * DTO cho yêu cầu cập nhật thông tin người dùng
 */
export class ZaloUpdateUserRequestDto {
  @ApiProperty({
    description: 'ID của người dùng',
    example: 'user_123456789',
  })
  @IsString()
  @IsNotEmpty()
  user_id: string;

  @ApiPropertyOptional({
    description: 'Tên hiển thị mới',
    example: 'Nguyễn Văn B',
  })
  @IsString()
  @IsOptional()
  display_name?: string;

  @ApiPropertyOptional({
    description: 'Ghi chú về người dùng',
    example: 'Khách hàng thân thiết',
  })
  @IsString()
  @IsOptional()
  note?: string;

  @ApiPropertyOptional({
    description: 'Thông tin bổ sung',
    example: { preference: 'mobile', location: 'HCM' },
  })
  @IsObject()
  @IsOptional()
  metadata?: Record<string, any>;
}

/**
 * DTO cho thông tin tùy biến của người dùng
 */
export class ZaloUserCustomInfoDto {
  @ApiProperty({
    description: 'ID của người dùng',
    example: 'user_123456789',
  })
  user_id: string;

  @ApiProperty({
    description: 'Thông tin tùy biến (key-value pairs)',
    example: {
      company: 'ABC Corp',
      position: 'Manager',
      interests: ['technology', 'sports'],
    },
  })
  custom_fields: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Thời gian cập nhật cuối cùng (Unix timestamp)',
    example: 1625097600000,
  })
  last_updated?: number;
}

/**
 * DTO cho yêu cầu cập nhật thông tin tùy biến
 */
export class ZaloUpdateCustomInfoRequestDto {
  @ApiProperty({
    description: 'ID của người dùng',
    example: 'user_123456789',
  })
  @IsString()
  @IsNotEmpty()
  user_id: string;

  @ApiProperty({
    description: 'Thông tin tùy biến cần cập nhật',
    example: {
      company: 'XYZ Corp',
      position: 'Senior Manager',
      interests: ['technology', 'music'],
    },
  })
  @IsObject()
  @IsNotEmpty()
  custom_fields: Record<string, any>;
}

/**
 * DTO cho tham số phân trang danh sách người dùng
 */
export class ZaloUserListParamsDto {
  @ApiPropertyOptional({
    description: 'Vị trí bắt đầu',
    example: 0,
    minimum: 0,
  })
  @IsNumber()
  @IsOptional()
  @Min(0)
  offset?: number = 0;

  @ApiPropertyOptional({
    description: 'Số lượng người dùng tối đa trả về (1-50)',
    example: 20,
    minimum: 1,
    maximum: 50,
  })
  @IsNumber()
  @IsOptional()
  @Min(1)
  @Max(50)
  count?: number = 20;

  @ApiPropertyOptional({
    description: 'ID nhãn để lọc người dùng',
    example: 'label_123456789',
  })
  @IsString()
  @IsOptional()
  label_id?: string;
}

/**
 * DTO cho tham số phân trang danh sách nhãn
 */
export class ZaloLabelListParamsDto {
  @ApiPropertyOptional({
    description: 'Vị trí bắt đầu',
    example: 0,
    minimum: 0,
  })
  @IsNumber()
  @IsOptional()
  @Min(0)
  offset?: number = 0;

  @ApiPropertyOptional({
    description: 'Số lượng nhãn tối đa trả về (1-50)',
    example: 20,
    minimum: 1,
    maximum: 50,
  })
  @IsNumber()
  @IsOptional()
  @Min(1)
  @Max(50)
  count?: number = 20;
}

/**
 * DTO cho tham số tìm kiếm người dùng
 */
export class ZaloUserSearchParamsDto {
  @ApiProperty({
    description: 'Từ khóa tìm kiếm (tên hoặc ID)',
    example: 'Nguyễn Văn',
  })
  @IsString()
  @IsNotEmpty()
  query: string;

  @ApiPropertyOptional({
    description: 'Vị trí bắt đầu',
    example: 0,
    minimum: 0,
  })
  @IsNumber()
  @IsOptional()
  @Min(0)
  offset?: number = 0;

  @ApiPropertyOptional({
    description: 'Số lượng kết quả tối đa (1-50)',
    example: 20,
    minimum: 1,
    maximum: 50,
  })
  @IsNumber()
  @IsOptional()
  @Min(1)
  @Max(50)
  count?: number = 20;
}

/**
 * DTO cho phản hồi thành công
 */
export class ZaloSuccessResponseDto {
  @ApiProperty({
    description: 'Trạng thái thành công',
    example: true,
  })
  success: boolean;
}
