import {
  ZaloWebhookEvent,
  ZaloUserSendTextEvent,
  ZaloUserSendImageEvent,
  ZaloUserClickChatNowEvent,
  ZaloUserReactionEvent,
  ZaloUserFollowEvent,
  ZaloWebhookEventType,
  isUserMessageEvent,
  isInteractionEvent,
  isFollowEvent,
  isOaMessageEvent
} from './zalo-webhook.dto';

describe('ZaloWebhookDTO', () => {
  describe('Type Guards', () => {
    it('should correctly identify user message events', () => {
      const textEvent: ZaloUserSendTextEvent = {
        event_id: 'evt_123',
        event_name: 'user_send_text',
        timestamp: 1640995200,
        oa_id: 'oa_123',
        data: {
          sender: { id: 'user_123', name: 'Test User' },
          recipient: { id: 'oa_123' },
          message: {
            msg_id: 'msg_123',
            text: 'Hello',
            timestamp: 1640995200
          }
        }
      };

      expect(isUserMessageEvent(textEvent)).toBe(true);
      expect(isInteractionEvent(textEvent)).toBe(false);
      expect(isFollowEvent(textEvent)).toBe(false);
      expect(isOaMessageEvent(textEvent)).toBe(false);
    });

    it('should correctly identify interaction events', () => {
      const clickEvent: ZaloUserClickChatNowEvent = {
        event_id: 'evt_456',
        event_name: 'user_click_chatnow',
        timestamp: 1640995200,
        oa_id: 'oa_123',
        data: {
          sender: { id: 'user_123', name: 'Test User' },
          recipient: { id: 'oa_123' },
          timestamp: 1640995200
        }
      };

      expect(isUserMessageEvent(clickEvent)).toBe(false);
      expect(isInteractionEvent(clickEvent)).toBe(true);
      expect(isFollowEvent(clickEvent)).toBe(false);
      expect(isOaMessageEvent(clickEvent)).toBe(false);
    });

    it('should correctly identify follow events', () => {
      const followEvent: ZaloUserFollowEvent = {
        event_id: 'evt_789',
        event_name: 'user_follow',
        timestamp: 1640995200,
        oa_id: 'oa_123',
        data: {
          follower: { id: 'user_123', name: 'Test User' },
          timestamp: 1640995200
        }
      };

      expect(isUserMessageEvent(followEvent)).toBe(false);
      expect(isInteractionEvent(followEvent)).toBe(false);
      expect(isFollowEvent(followEvent)).toBe(true);
      expect(isOaMessageEvent(followEvent)).toBe(false);
    });
  });

  describe('Event Type Enum', () => {
    it('should have correct event type values', () => {
      expect(ZaloWebhookEventType.USER_SEND_TEXT).toBe('user_send_text');
      expect(ZaloWebhookEventType.USER_SEND_IMAGE).toBe('user_send_image');
      expect(ZaloWebhookEventType.USER_CLICK_CHATNOW).toBe('user_click_chatnow');
      expect(ZaloWebhookEventType.USER_REACTION).toBe('user_reaction');
      expect(ZaloWebhookEventType.USER_FOLLOW).toBe('user_follow');
      expect(ZaloWebhookEventType.USER_UNFOLLOW).toBe('user_unfollow');
    });
  });

  describe('Event Structure Validation', () => {
    it('should validate user send text event structure', () => {
      const event: ZaloUserSendTextEvent = {
        event_id: 'evt_text_123',
        event_name: 'user_send_text',
        timestamp: 1640995200,
        oa_id: 'oa_123456789',
        data: {
          sender: {
            id: 'user_123456789',
            name: 'Nguyễn Văn A',
            avatar: 'https://example.com/avatar.jpg'
          },
          recipient: {
            id: 'oa_123456789'
          },
          message: {
            msg_id: 'msg_123456789',
            text: 'Xin chào!',
            timestamp: 1640995200
          }
        }
      };

      expect(event.event_name).toBe('user_send_text');
      expect(event.data.sender.id).toBe('user_123456789');
      expect(event.data.message.text).toBe('Xin chào!');
    });

    it('should validate user send image event structure', () => {
      const event: ZaloUserSendImageEvent = {
        event_id: 'evt_image_123',
        event_name: 'user_send_image',
        timestamp: 1640995200,
        oa_id: 'oa_123456789',
        data: {
          sender: {
            id: 'user_123456789',
            name: 'Nguyễn Văn A'
          },
          recipient: {
            id: 'oa_123456789'
          },
          message: {
            msg_id: 'msg_image_123',
            attachments: [{
              type: 'image',
              payload: {
                url: 'https://example.com/image.jpg',
                size: 1024000,
                type: 'image/jpeg'
              }
            }],
            timestamp: 1640995200
          }
        }
      };

      expect(event.event_name).toBe('user_send_image');
      expect(event.data.message.attachments).toHaveLength(1);
      expect(event.data.message.attachments[0].type).toBe('image');
      expect(event.data.message.attachments[0].payload.url).toBe('https://example.com/image.jpg');
    });

    it('should validate user reaction event structure', () => {
      const event: ZaloUserReactionEvent = {
        event_id: 'evt_reaction_123',
        event_name: 'user_reaction',
        timestamp: 1640995200,
        oa_id: 'oa_123456789',
        data: {
          sender: {
            id: 'user_123456789',
            name: 'Nguyễn Văn A'
          },
          recipient: {
            id: 'oa_123456789'
          },
          reaction: {
            msg_id: 'msg_123456789',
            reaction: 'heart',
            action: 'add'
          },
          timestamp: 1640995200
        }
      };

      expect(event.event_name).toBe('user_reaction');
      expect(event.data.reaction.reaction).toBe('heart');
      expect(event.data.reaction.action).toBe('add');
    });

    it('should validate location message structure', () => {
      const locationAttachment = {
        type: 'location' as const,
        payload: {
          coordinates: {
            latitude: 10.762622,
            longitude: 106.660172
          }
        }
      };

      expect(locationAttachment.type).toBe('location');
      expect(locationAttachment.payload.coordinates.latitude).toBe(10.762622);
      expect(locationAttachment.payload.coordinates.longitude).toBe(106.660172);
    });

    it('should validate sticker message structure', () => {
      const stickerAttachment = {
        type: 'sticker' as const,
        payload: {
          sticker_id: 'sticker_123',
          sticker_category: 'emoji'
        }
      };

      expect(stickerAttachment.type).toBe('sticker');
      expect(stickerAttachment.payload.sticker_id).toBe('sticker_123');
      expect(stickerAttachment.payload.sticker_category).toBe('emoji');
    });
  });

  describe('Union Types', () => {
    it('should accept all valid webhook events in union type', () => {
      const events: ZaloWebhookEvent[] = [
        {
          event_id: 'evt_1',
          event_name: 'user_send_text',
          timestamp: 1640995200,
          oa_id: 'oa_123',
          data: {
            sender: { id: 'user_123' },
            recipient: { id: 'oa_123' },
            message: { msg_id: 'msg_1', text: 'Hello', timestamp: 1640995200 }
          }
        } as ZaloUserSendTextEvent,
        {
          event_id: 'evt_2',
          event_name: 'user_follow',
          timestamp: 1640995200,
          oa_id: 'oa_123',
          data: {
            follower: { id: 'user_123' },
            timestamp: 1640995200
          }
        } as ZaloUserFollowEvent
      ];

      expect(events).toHaveLength(2);
      expect(events[0].event_name).toBe('user_send_text');
      expect(events[1].event_name).toBe('user_follow');
    });
  });

  describe('Edge Cases', () => {
    it('should handle events with minimal data', () => {
      const minimalEvent: ZaloUserSendTextEvent = {
        event_id: 'evt_minimal',
        event_name: 'user_send_text',
        timestamp: 1640995200,
        oa_id: 'oa_123',
        data: {
          sender: { id: 'user_123' },
          recipient: { id: 'oa_123' },
          message: {
            msg_id: 'msg_minimal',
            text: '',
            timestamp: 1640995200
          }
        }
      };

      expect(minimalEvent.data.message.text).toBe('');
      expect(minimalEvent.data.sender.name).toBeUndefined();
      expect(minimalEvent.data.sender.avatar).toBeUndefined();
    });

    it('should handle events with optional fields', () => {
      const eventWithOptionals: ZaloUserSendImageEvent = {
        event_id: 'evt_optional',
        event_name: 'user_send_image',
        timestamp: 1640995200,
        oa_id: 'oa_123',
        data: {
          sender: {
            id: 'user_123',
            name: 'Test User',
            avatar: 'https://example.com/avatar.jpg'
          },
          recipient: { id: 'oa_123' },
          message: {
            msg_id: 'msg_optional',
            attachments: [{
              type: 'image',
              payload: {
                url: 'https://example.com/image.jpg',
                size: 1024000,
                type: 'image/jpeg'
              }
            }],
            timestamp: 1640995200
          }
        }
      };

      expect(eventWithOptionals.data.sender.name).toBe('Test User');
      expect(eventWithOptionals.data.sender.avatar).toBe('https://example.com/avatar.jpg');
    });
  });
});
