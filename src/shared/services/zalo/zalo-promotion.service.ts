import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppException, ErrorCode } from '@common/exceptions';
import { ZaloService } from './zalo.service';

/**
 * Service xử lý các API tin nhắn truyền thông của Zalo Official Account
 * 
 * ĐIỀU KIỆN GỬI TIN TRUYỀN THÔNG:
 * 
 * 1. THỜI GIAN GỬI:
 *    - Chỉ được gửi trong khung giờ từ 8:00 - 22:00 hàng ngày
 *    - Không được gửi vào các ngày lễ, tết theo quy định của Zalo
 *    - <PERSON>ải có khoảng cách ít nhất 24 giờ giữa các tin nhắn truyền thông
 * 
 * 2. TẦN SUẤT GỬI:
 *    - Tối đa 1 tin nhắn truyền thông/tuần cho mỗi người dùng
 *    - Không được gửi quá 3 tin nhắn truyền thông/tháng cho mỗi người dùng
 *    - <PERSON><PERSON> mới chỉ được gửi 1 tin/tháng trong 3 tháng đầu
 * 
 * 3. NỘI DUNG TIN NHẮN:
 *    - Phải là nội dung truyền thông, quảng cáo, khuyến mãi
 *    - Không được chứa nội dung vi phạm pháp luật, đạo đức
 *    - Phải có thông tin liên hệ rõ ràng của doanh nghiệp
 *    - Phải có cách thức từ chối nhận tin nhắn (unsubscribe)
 * 
 * 4. NGƯỜI DÙNG:
 *    - Người dùng phải đã follow OA
 *    - Người dùng không được block OA
 *    - Người dùng phải đồng ý nhận tin nhắn truyền thông
 *    - Người dùng có thể từ chối nhận tin nhắn truyền thông bất kỳ lúc nào
 * 
 * 5. OFFICIAL ACCOUNT:
 *    - OA phải được xác minh (verified)
 *    - OA phải có quyền gửi tin truyền thông được Zalo cấp phép
 *    - OA không được vi phạm chính sách của Zalo
 *    - OA phải có thông tin doanh nghiệp đầy đủ và chính xác
 * 
 * 6. TEMPLATE VÀ ĐỊNH DẠNG:
 *    - Phải sử dụng template được Zalo phê duyệt trước
 *    - Template phải tuân thủ format chuẩn của tin truyền thông
 *    - Không được sử dụng template của loại tin nhắn khác
 * 
 * 7. GIÁM SÁT VÀ KIỂM DUYỆT:
 *    - Tất cả tin nhắn truyền thông đều được Zalo giám sát
 *    - Nội dung có thể bị kiểm duyệt trước khi gửi
 *    - Vi phạm có thể dẫn đến tạm khóa hoặc khóa vĩnh viễn quyền gửi tin
 * 
 * LỖI THƯỜNG GẶP:
 * - 2001: Người dùng chưa follow OA hoặc đã unfollow
 * - 2002: Người dùng đã từ chối nhận tin nhắn truyền thông
 * - 2003: Vượt quá thời gian cho phép gửi tin (8:00-22:00)
 * - 2004: Vượt quá tần suất cho phép (1 tin/tuần, 3 tin/tháng)
 * - 2005: Template chưa được phê duyệt hoặc không hợp lệ
 * - 2006: Nội dung tin nhắn vi phạm chính sách
 * - 2007: OA chưa có quyền gửi tin truyền thông
 * - 2008: Đang trong thời gian bị tạm khóa gửi tin truyền thông
 */
@Injectable()
export class ZaloPromotionService {
  private readonly logger = new Logger(ZaloPromotionService.name);
  private readonly promotionApiUrl = 'https://openapi.zalo.me/v2.0/oa/message/promotion';

  constructor(
    private readonly zaloService: ZaloService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Gửi tin nhắn truyền thông cá nhân
   * @param accessToken Access token của Official Account
   * @param userId ID của người dùng Zalo
   * @param templateId ID của template truyền thông đã được phê duyệt
   * @param templateData Dữ liệu để điền vào template
   * @param mode Chế độ gửi tin nhắn (development/production)
   * @returns ID của tin nhắn
   */
  async sendPromotionMessage(
    accessToken: string,
    userId: string,
    templateId: string,
    templateData: Record<string, string>,
    mode: 'development' | 'production' = 'production',
  ): Promise<{ message_id: string }> {
    try {
      // Validate thời gian gửi (8:00 - 22:00)
      this.validateSendingTime();

      // Validate template data
      this.validateTemplateData(templateData);

      const data = {
        recipient: {
          user_id: userId,
        },
        message: {
          attachment: {
            type: 'template',
            payload: {
              template_type: 'promotion',
              template_id: templateId,
              template_data: templateData,
              mode: mode,
            },
          },
        },
      };

      this.logger.debug(`Sending promotion message to user ${userId} with template ${templateId}`);
      this.logger.debug(`Template data:`, JSON.stringify(templateData, null, 2));

      const result = await this.zaloService.post<{ message_id: string }>(
        this.promotionApiUrl,
        accessToken,
        data,
      );

      this.logger.log(`Promotion message sent successfully. Message ID: ${result.message_id}`);
      return result;

    } catch (error) {
      this.logger.error(`Error sending promotion message: ${error.message}`, error.stack);
      
      // Handle specific Zalo API errors
      if (error.response?.data?.error) {
        const zaloError = error.response.data.error;
        switch (zaloError) {
          case 2001:
            throw new AppException(
              ErrorCode.VALIDATION_ERROR,
              'Người dùng chưa follow Official Account hoặc đã unfollow'
            );
          case 2002:
            throw new AppException(
              ErrorCode.VALIDATION_ERROR,
              'Người dùng đã từ chối nhận tin nhắn truyền thông'
            );
          case 2003:
            throw new AppException(
              ErrorCode.VALIDATION_ERROR,
              'Chỉ được gửi tin nhắn truyền thông trong khung giờ 8:00-22:00'
            );
          case 2004:
            throw new AppException(
              ErrorCode.VALIDATION_ERROR,
              'Vượt quá tần suất cho phép (1 tin/tuần, 3 tin/tháng)'
            );
          case 2005:
            throw new AppException(
              ErrorCode.VALIDATION_ERROR,
              'Template chưa được phê duyệt hoặc không hợp lệ'
            );
          case 2006:
            throw new AppException(
              ErrorCode.VALIDATION_ERROR,
              'Nội dung tin nhắn vi phạm chính sách Zalo'
            );
          case 2007:
            throw new AppException(
              ErrorCode.VALIDATION_ERROR,
              'Official Account chưa có quyền gửi tin truyền thông'
            );
          case 2008:
            throw new AppException(
              ErrorCode.VALIDATION_ERROR,
              'Đang trong thời gian bị tạm khóa gửi tin truyền thông'
            );
          default:
            throw new AppException(
              ErrorCode.EXTERNAL_SERVICE_ERROR,
              `Lỗi từ Zalo API: ${error.response.data.message || 'Unknown error'}`
            );
        }
      }

      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi gửi tin nhắn truyền thông',
      );
    }
  }

  /**
   * Gửi tin nhắn khuyến mãi
   * @param accessToken Access token của Official Account
   * @param userId ID của người dùng Zalo
   * @param promotionData Thông tin khuyến mãi
   * @returns ID của tin nhắn
   */
  async sendPromotionCampaign(
    accessToken: string,
    userId: string,
    promotionData: {
      title: string;
      description: string;
      discountPercent?: string;
      originalPrice?: string;
      salePrice?: string;
      validUntil?: string;
      promoCode?: string;
      imageUrl?: string;
      actionUrl?: string;
    },
  ): Promise<{ message_id: string }> {
    try {
      // Template ID cho khuyến mãi (cần được cấu hình trong env)
      const templateId = this.configService.get<string>('ZALO_PROMOTION_CAMPAIGN_TEMPLATE_ID');
      
      if (!templateId) {
        throw new AppException(
          ErrorCode.CONFIGURATION_ERROR,
          'Template ID cho tin nhắn khuyến mãi chưa được cấu hình'
        );
      }

      const templateData = {
        title: promotionData.title,
        description: promotionData.description,
        ...(promotionData.discountPercent && { discount_percent: promotionData.discountPercent }),
        ...(promotionData.originalPrice && { original_price: promotionData.originalPrice }),
        ...(promotionData.salePrice && { sale_price: promotionData.salePrice }),
        ...(promotionData.validUntil && { valid_until: promotionData.validUntil }),
        ...(promotionData.promoCode && { promo_code: promotionData.promoCode }),
        ...(promotionData.imageUrl && { image_url: promotionData.imageUrl }),
        ...(promotionData.actionUrl && { action_url: promotionData.actionUrl }),
      };

      return await this.sendPromotionMessage(
        accessToken,
        userId,
        templateId,
        templateData,
      );
    } catch (error) {
      this.logger.error(`Error sending promotion campaign: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi gửi tin nhắn khuyến mãi',
      );
    }
  }

  /**
   * Gửi tin nhắn thông báo sự kiện
   * @param accessToken Access token của Official Account
   * @param userId ID của người dùng Zalo
   * @param eventData Thông tin sự kiện
   * @returns ID của tin nhắn
   */
  async sendEventNotification(
    accessToken: string,
    userId: string,
    eventData: {
      eventName: string;
      eventDate: string;
      eventTime?: string;
      location?: string;
      description?: string;
      registrationUrl?: string;
      imageUrl?: string;
    },
  ): Promise<{ message_id: string }> {
    try {
      // Template ID cho thông báo sự kiện (cần được cấu hình trong env)
      const templateId = this.configService.get<string>('ZALO_EVENT_NOTIFICATION_TEMPLATE_ID');
      
      if (!templateId) {
        throw new AppException(
          ErrorCode.CONFIGURATION_ERROR,
          'Template ID cho thông báo sự kiện chưa được cấu hình'
        );
      }

      const templateData = {
        event_name: eventData.eventName,
        event_date: eventData.eventDate,
        ...(eventData.eventTime && { event_time: eventData.eventTime }),
        ...(eventData.location && { location: eventData.location }),
        ...(eventData.description && { description: eventData.description }),
        ...(eventData.registrationUrl && { registration_url: eventData.registrationUrl }),
        ...(eventData.imageUrl && { image_url: eventData.imageUrl }),
      };

      return await this.sendPromotionMessage(
        accessToken,
        userId,
        templateId,
        templateData,
      );
    } catch (error) {
      this.logger.error(`Error sending event notification: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi gửi thông báo sự kiện',
      );
    }
  }

  /**
   * Validate thời gian gửi tin nhắn (8:00 - 22:00)
   */
  private validateSendingTime(): void {
    const now = new Date();
    const hour = now.getHours();

    if (hour < 8 || hour >= 22) {
      throw new AppException(
        ErrorCode.VALIDATION_ERROR,
        'Tin nhắn truyền thông chỉ được gửi trong khung giờ từ 8:00 đến 22:00'
      );
    }

    // Kiểm tra ngày lễ (có thể mở rộng thêm)
    const isHoliday = this.checkIfHoliday(now);
    if (isHoliday) {
      this.logger.warn('Sending promotion message on holiday, this may be restricted');
    }
  }

  /**
   * Kiểm tra có phải ngày lễ không
   * @param date Ngày cần kiểm tra
   * @returns true nếu là ngày lễ
   */
  private checkIfHoliday(date: Date): boolean {
    // Danh sách ngày lễ cố định (có thể mở rộng)
    const holidays = [
      '01-01', // Tết Dương lịch
      '30-04', // Giải phóng miền Nam
      '01-05', // Quốc tế Lao động
      '02-09', // Quốc khánh
    ];

    const monthDay = `${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
    return holidays.includes(monthDay);
  }

  /**
   * Validate template data để đảm bảo tuân thủ quy định
   * @param templateData Dữ liệu template cần validate
   */
  private validateTemplateData(templateData: Record<string, string>): void {
    // Kiểm tra các trường bắt buộc
    if (!templateData || Object.keys(templateData).length === 0) {
      throw new AppException(
        ErrorCode.VALIDATION_ERROR,
        'Dữ liệu template không được để trống'
      );
    }

    // Kiểm tra độ dài của các giá trị
    for (const [key, value] of Object.entries(templateData)) {
      if (typeof value !== 'string') {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          `Giá trị của trường ${key} phải là chuỗi`
        );
      }

      if (value.length > 1000) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          `Giá trị của trường ${key} không được vượt quá 1000 ký tự`
        );
      }
    }

    // Kiểm tra có thông tin liên hệ không
    const allText = Object.values(templateData).join(' ').toLowerCase();
    const hasContactInfo = /(\d{10,11}|@|\w+\.\w+)/.test(allText);
    
    if (!hasContactInfo) {
      this.logger.warn('Template data may be missing contact information');
    }

    // Kiểm tra có cách thức unsubscribe không
    const hasUnsubscribe = /unsubscribe|từ chối|hủy nhận|dừng nhận/.test(allText);
    
    if (!hasUnsubscribe) {
      this.logger.warn('Template data may be missing unsubscribe information');
    }
  }
}
