import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { ZaloUserInfoFieldsService } from './zalo-user-info-fields.service';
import { ZaloService } from './zalo.service';
import { AppException, ErrorCode } from '@common/exceptions';
import {
  ZaloUserInfoFieldType,
  ZaloUserInfoField,
  ZaloUserInfoFieldList,
  ZaloCreateUserInfoFieldRequest,
  ZaloUpdateUserInfoFieldRequest,
} from './zalo.interface';

describe('ZaloUserInfoFieldsService', () => {
  let service: ZaloUserInfoFieldsService;
  let zaloService: jest.Mocked<ZaloService>;
  let configService: jest.Mocked<ConfigService>;

  const mockAccessToken = 'test_access_token';
  const mockFieldId = 'field_123456789';

  const mockUserInfoField: ZaloUserInfoField = {
    field_id: mockFieldId,
    field_name: 'Công ty',
    field_type: ZaloUserInfoFieldType.TEXT,
    description: 'Tên công ty nơi khách hàng làm việc',
    required: false,
    default_value: 'Không xác định',
    display_order: 1,
    is_system_field: false,
    created_time: 1625097600000,
    updated_time: 1625097600000,
  };

  const mockUserInfoFieldList: ZaloUserInfoFieldList = {
    fields: [mockUserInfoField],
    total: 1,
    has_more: false,
  };

  beforeEach(async () => {
    const mockZaloService = {
      get: jest.fn(),
      post: jest.fn(),
    };

    const mockConfigService = {
      get: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ZaloUserInfoFieldsService,
        {
          provide: ZaloService,
          useValue: mockZaloService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<ZaloUserInfoFieldsService>(ZaloUserInfoFieldsService);
    zaloService = module.get(ZaloService);
    configService = module.get(ConfigService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getUserInfoFields', () => {
    it('should get user info fields successfully', async () => {
      zaloService.get.mockResolvedValue(mockUserInfoFieldList);

      const result = await service.getUserInfoFields(mockAccessToken, 0, 20);

      expect(result).toEqual(mockUserInfoFieldList);
      expect(zaloService.get).toHaveBeenCalledWith(
        'https://openapi.zalo.me/v2.0/oa/user/field?offset=0&count=20',
        mockAccessToken,
      );
    });

    it('should handle errors when getting user info fields', async () => {
      const error = new Error('API Error');
      zaloService.get.mockRejectedValue(error);

      await expect(service.getUserInfoFields(mockAccessToken)).rejects.toThrow(AppException);
    });
  });

  describe('createUserInfoField', () => {
    it('should create user info field successfully', async () => {
      const createRequest: ZaloCreateUserInfoFieldRequest = {
        field_name: 'Công ty',
        field_type: ZaloUserInfoFieldType.TEXT,
        description: 'Tên công ty nơi khách hàng làm việc',
        required: false,
        default_value: 'Không xác định',
        display_order: 1,
      };

      zaloService.post.mockResolvedValue(mockUserInfoField);

      const result = await service.createUserInfoField(mockAccessToken, createRequest);

      expect(result).toEqual(mockUserInfoField);
      expect(zaloService.post).toHaveBeenCalledWith(
        'https://openapi.zalo.me/v2.0/oa/user/field/create',
        mockAccessToken,
        createRequest,
      );
    });

    it('should handle errors when creating user info field', async () => {
      const createRequest: ZaloCreateUserInfoFieldRequest = {
        field_name: 'Công ty',
        field_type: ZaloUserInfoFieldType.TEXT,
      };
      const error = new Error('API Error');
      zaloService.post.mockRejectedValue(error);

      await expect(service.createUserInfoField(mockAccessToken, createRequest)).rejects.toThrow(AppException);
    });
  });

  describe('updateUserInfoField', () => {
    it('should update user info field successfully', async () => {
      const updateRequest: ZaloUpdateUserInfoFieldRequest = {
        field_name: 'Công ty làm việc',
        description: 'Tên công ty nơi khách hàng đang làm việc',
        required: true,
      };

      const updatedField = { ...mockUserInfoField, ...updateRequest };
      zaloService.post.mockResolvedValue(updatedField);

      const result = await service.updateUserInfoField(mockAccessToken, mockFieldId, updateRequest);

      expect(result).toEqual(updatedField);
      expect(zaloService.post).toHaveBeenCalledWith(
        `https://openapi.zalo.me/v2.0/oa/user/field/${mockFieldId}/update`,
        mockAccessToken,
        updateRequest,
      );
    });

    it('should handle errors when updating user info field', async () => {
      const updateRequest: ZaloUpdateUserInfoFieldRequest = {
        field_name: 'Công ty làm việc',
      };
      const error = new Error('API Error');
      zaloService.post.mockRejectedValue(error);

      await expect(service.updateUserInfoField(mockAccessToken, mockFieldId, updateRequest)).rejects.toThrow(AppException);
    });
  });

  describe('deleteUserInfoField', () => {
    it('should delete user info field successfully', async () => {
      const successResponse = { success: true };
      zaloService.post.mockResolvedValue(successResponse);

      const result = await service.deleteUserInfoField(mockAccessToken, mockFieldId);

      expect(result).toEqual(successResponse);
      expect(zaloService.post).toHaveBeenCalledWith(
        `https://openapi.zalo.me/v2.0/oa/user/field/${mockFieldId}/delete`,
        mockAccessToken,
      );
    });

    it('should handle errors when deleting user info field', async () => {
      const error = new Error('API Error');
      zaloService.post.mockRejectedValue(error);

      await expect(service.deleteUserInfoField(mockAccessToken, mockFieldId)).rejects.toThrow(AppException);
    });
  });

  describe('getUserInfoFieldDetail', () => {
    it('should get user info field detail successfully', async () => {
      zaloService.get.mockResolvedValue(mockUserInfoField);

      const result = await service.getUserInfoFieldDetail(mockAccessToken, mockFieldId);

      expect(result).toEqual(mockUserInfoField);
      expect(zaloService.get).toHaveBeenCalledWith(
        `https://openapi.zalo.me/v2.0/oa/user/field/${mockFieldId}`,
        mockAccessToken,
      );
    });

    it('should handle errors when getting user info field detail', async () => {
      const error = new Error('API Error');
      zaloService.get.mockRejectedValue(error);

      await expect(service.getUserInfoFieldDetail(mockAccessToken, mockFieldId)).rejects.toThrow(AppException);
    });
  });
});
