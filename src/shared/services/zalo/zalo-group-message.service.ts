import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppException, ErrorCode } from '@common/exceptions';
import { ZaloService } from './zalo.service';
import {
  ZaloGroupMessage,
  ZaloGroupTextMessage,
  ZaloGroupImageMessage,
  ZaloGroupFileMessage,
  ZaloGroupStickerMessage,
  ZaloGroupMentionMessage,
  ZaloGroupMessageResult,
  ZaloGroupInfo,
  ZaloGroupMember,
} from './zalo.interface';

/**
 * Service xử lý các API tin nhắn nhóm của Zalo Official Account (Group Message Framework - GMF)
 * 
 * ĐIỀU KIỆN SỬ DỤNG ZALO GMF:
 * 
 * 1. ĐIỀU KIỆN CẦN CÓ ĐỂ GỬI TIN NHẮN NHÓM (Opt-in condition):
 *    - OA phải có được group_id của nhóm chat
 *    - OA phải được thêm vào nhóm chat bởi admin nhóm
 *    - OA phải có quyền gửi tin nhắn trong nhóm (được cấp bởi admin nhóm)
 *    - Nhóm chat phải đang hoạt động (không bị khóa hoặc giải tán)
 * 
 * 2. QUYỀN TRUY CẬP:
 *    - Ứng dụng cần được cấp quyền quản lý thông tin nhóm chat
 *    - Access token phải có scope "manage_group" hoặc tương đương
 *    - OA phải được xác thực và có trạng thái hoạt động
 * 
 * 3. GIỚI HẠN VÀ RÀNG BUỘC:
 *    - Chỉ có thể gửi tin nhắn đến nhóm mà OA đã tham gia
 *    - Không thể gửi tin nhắn đến nhóm riêng tư mà OA chưa được mời
 *    - Tuân thủ giới hạn tần suất gửi tin nhắn của Zalo
 *    - Nội dung tin nhắn phải tuân thủ chính sách nội dung của Zalo
 * 
 * 4. LOẠI TIN NHẮN HỖ TRỢ:
 *    - Text message: Tin nhắn văn bản thuần túy
 *    - Image message: Tin nhắn hình ảnh (JPG, PNG, GIF - tối đa 5MB)
 *    - File message: Tin nhắn file đính kèm (tối đa 25MB)
 *    - Sticker message: Tin nhắn sticker từ bộ sưu tập Zalo
 *    - Mention message: Tin nhắn tag/mention thành viên cụ thể
 * 
 * 5. YÊU CẦU KỸ THUẬT:
 *    - Sử dụng HTTPS cho tất cả API calls
 *    - Content-Type: application/json cho text/mention messages
 *    - Content-Type: multipart/form-data cho file/image uploads
 *    - Access token phải được truyền trong header Authorization
 */
@Injectable()
export class ZaloGroupMessageService {
  private readonly logger = new Logger(ZaloGroupMessageService.name);
  private readonly baseApiUrl = 'https://openapi.zalo.me/v2.0/oa';

  constructor(
    private readonly zaloService: ZaloService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Gửi tin nhắn văn bản đến nhóm
   * 
   * ĐIỀU KIỆN:
   * - OA phải đã tham gia nhóm
   * - Có quyền gửi tin nhắn trong nhóm
   * - Nội dung không vi phạm chính sách Zalo
   * 
   * @param accessToken Access token của Official Account
   * @param groupId ID của nhóm chat
   * @param message Nội dung tin nhắn văn bản
   * @returns Kết quả gửi tin nhắn
   */
  async sendTextMessage(
    accessToken: string,
    groupId: string,
    message: ZaloGroupTextMessage,
  ): Promise<ZaloGroupMessageResult> {
    try {
      this.logger.debug(`Sending text message to group ${groupId}`);
      
      const data = {
        group_id: groupId,
        message: {
          text: message.text,
        },
      };

      return await this.zaloService.post<ZaloGroupMessageResult>(
        `${this.baseApiUrl}/group/message`,
        accessToken,
        data,
      );
    } catch (error) {
      this.logger.error(`Error sending text message to group: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi gửi tin nhắn văn bản đến nhóm',
      );
    }
  }

  /**
   * Gửi tin nhắn hình ảnh đến nhóm
   * 
   * ĐIỀU KIỆN:
   * - OA phải đã tham gia nhóm
   * - Hình ảnh phải được upload trước qua API upload
   * - Định dạng: JPG, PNG, GIF
   * - Kích thước tối đa: 5MB
   * 
   * @param accessToken Access token của Official Account
   * @param groupId ID của nhóm chat
   * @param message Tin nhắn hình ảnh
   * @returns Kết quả gửi tin nhắn
   */
  async sendImageMessage(
    accessToken: string,
    groupId: string,
    message: ZaloGroupImageMessage,
  ): Promise<ZaloGroupMessageResult> {
    try {
      this.logger.debug(`Sending image message to group ${groupId}`);
      
      const data = {
        group_id: groupId,
        message: {
          attachment: {
            type: 'image',
            payload: {
              url: message.url,
              ...(message.caption && { caption: message.caption }),
            },
          },
        },
      };

      return await this.zaloService.post<ZaloGroupMessageResult>(
        `${this.baseApiUrl}/group/message`,
        accessToken,
        data,
      );
    } catch (error) {
      this.logger.error(`Error sending image message to group: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi gửi tin nhắn hình ảnh đến nhóm',
      );
    }
  }

  /**
   * Gửi tin nhắn file đến nhóm
   * 
   * ĐIỀU KIỆN:
   * - OA phải đã tham gia nhóm
   * - File phải được upload trước qua API upload
   * - Kích thước tối đa: 25MB
   * - Định dạng được hỗ trợ: PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, TXT, ZIP, RAR
   * 
   * @param accessToken Access token của Official Account
   * @param groupId ID của nhóm chat
   * @param message Tin nhắn file
   * @returns Kết quả gửi tin nhắn
   */
  async sendFileMessage(
    accessToken: string,
    groupId: string,
    message: ZaloGroupFileMessage,
  ): Promise<ZaloGroupMessageResult> {
    try {
      this.logger.debug(`Sending file message to group ${groupId}`);
      
      const data = {
        group_id: groupId,
        message: {
          attachment: {
            type: 'file',
            payload: {
              url: message.url,
              filename: message.filename,
            },
          },
        },
      };

      return await this.zaloService.post<ZaloGroupMessageResult>(
        `${this.baseApiUrl}/group/message`,
        accessToken,
        data,
      );
    } catch (error) {
      this.logger.error(`Error sending file message to group: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi gửi tin nhắn file đến nhóm',
      );
    }
  }

  /**
   * Gửi tin nhắn sticker đến nhóm
   * 
   * ĐIỀU KIỆN:
   * - OA phải đã tham gia nhóm
   * - Sticker ID phải hợp lệ và có trong bộ sưu tập Zalo
   * - Sticker không vi phạm chính sách nội dung
   * 
   * @param accessToken Access token của Official Account
   * @param groupId ID của nhóm chat
   * @param message Tin nhắn sticker
   * @returns Kết quả gửi tin nhắn
   */
  async sendStickerMessage(
    accessToken: string,
    groupId: string,
    message: ZaloGroupStickerMessage,
  ): Promise<ZaloGroupMessageResult> {
    try {
      this.logger.debug(`Sending sticker message to group ${groupId}`);
      
      const data = {
        group_id: groupId,
        message: {
          attachment: {
            type: 'sticker',
            payload: {
              sticker_id: message.sticker_id,
            },
          },
        },
      };

      return await this.zaloService.post<ZaloGroupMessageResult>(
        `${this.baseApiUrl}/group/message`,
        accessToken,
        data,
      );
    } catch (error) {
      this.logger.error(`Error sending sticker message to group: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi gửi tin nhắn sticker đến nhóm',
      );
    }
  }

  /**
   * Gửi tin nhắn mention đến nhóm
   *
   * ĐIỀU KIỆN:
   * - OA phải đã tham gia nhóm
   * - Các user ID được mention phải là thành viên của nhóm
   * - Không được mention quá nhiều người trong một tin nhắn (giới hạn thường là 5-10 người)
   *
   * @param accessToken Access token của Official Account
   * @param groupId ID của nhóm chat
   * @param message Tin nhắn mention
   * @returns Kết quả gửi tin nhắn
   */
  async sendMentionMessage(
    accessToken: string,
    groupId: string,
    message: ZaloGroupMentionMessage,
  ): Promise<ZaloGroupMessageResult> {
    try {
      this.logger.debug(`Sending mention message to group ${groupId}`);

      const data = {
        group_id: groupId,
        message: {
          text: message.text,
          mention_uids: message.mention_uids,
        },
      };

      return await this.zaloService.post<ZaloGroupMessageResult>(
        `${this.baseApiUrl}/group/message`,
        accessToken,
        data,
      );
    } catch (error) {
      this.logger.error(`Error sending mention message to group: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi gửi tin nhắn mention đến nhóm',
      );
    }
  }

  /**
   * Lấy thông tin nhóm chat
   *
   * ĐIỀU KIỆN:
   * - OA phải đã tham gia nhóm
   * - Có quyền xem thông tin nhóm
   *
   * @param accessToken Access token của Official Account
   * @param groupId ID của nhóm chat
   * @returns Thông tin chi tiết nhóm
   */
  async getGroupInfo(
    accessToken: string,
    groupId: string,
  ): Promise<ZaloGroupInfo> {
    try {
      this.logger.debug(`Getting group info for group ${groupId}`);

      return await this.zaloService.get<ZaloGroupInfo>(
        `${this.baseApiUrl}/group/${groupId}`,
        accessToken,
      );
    } catch (error) {
      this.logger.error(`Error getting group info: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy thông tin nhóm',
      );
    }
  }

  /**
   * Lấy danh sách thành viên nhóm
   *
   * ĐIỀU KIỆN:
   * - OA phải đã tham gia nhóm
   * - Có quyền xem danh sách thành viên
   *
   * @param accessToken Access token của Official Account
   * @param groupId ID của nhóm chat
   * @param offset Offset để phân trang (mặc định: 0)
   * @param count Số lượng thành viên tối đa trả về (mặc định: 50, tối đa: 100)
   * @returns Danh sách thành viên nhóm
   */
  async getGroupMembers(
    accessToken: string,
    groupId: string,
    offset: number = 0,
    count: number = 50,
  ): Promise<{ members: ZaloGroupMember[]; total: number }> {
    try {
      this.logger.debug(`Getting group members for group ${groupId}`);

      const params = new URLSearchParams();
      params.append('offset', offset.toString());
      params.append('count', Math.min(Math.max(count, 1), 100).toString());

      return await this.zaloService.get<{ members: ZaloGroupMember[]; total: number }>(
        `${this.baseApiUrl}/group/${groupId}/members?${params.toString()}`,
        accessToken,
      );
    } catch (error) {
      this.logger.error(`Error getting group members: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy danh sách thành viên nhóm',
      );
    }
  }

  /**
   * Lấy danh sách nhóm mà OA đã tham gia
   *
   * ĐIỀU KIỆN:
   * - OA phải có quyền truy cập danh sách nhóm
   *
   * @param accessToken Access token của Official Account
   * @param offset Offset để phân trang (mặc định: 0)
   * @param count Số lượng nhóm tối đa trả về (mặc định: 20, tối đa: 50)
   * @returns Danh sách nhóm
   */
  async getJoinedGroups(
    accessToken: string,
    offset: number = 0,
    count: number = 20,
  ): Promise<{ groups: ZaloGroupInfo[]; total: number }> {
    try {
      this.logger.debug(`Getting joined groups`);

      const params = new URLSearchParams();
      params.append('offset', offset.toString());
      params.append('count', Math.min(Math.max(count, 1), 50).toString());

      return await this.zaloService.get<{ groups: ZaloGroupInfo[]; total: number }>(
        `${this.baseApiUrl}/groups?${params.toString()}`,
        accessToken,
      );
    } catch (error) {
      this.logger.error(`Error getting joined groups: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy danh sách nhóm đã tham gia',
      );
    }
  }
}
