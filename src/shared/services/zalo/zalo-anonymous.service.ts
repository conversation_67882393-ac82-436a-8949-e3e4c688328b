import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppException, ErrorCode } from '@common/exceptions';
import { ZaloService } from './zalo.service';
import {
  ZaloAnonymousMessage,
  ZaloAnonymousTextMessage,
  ZaloAnonymousImageMessage,
  ZaloAnonymousFileMessage,
  ZaloAnonymousStickerMessage,
} from './zalo.interface';

/**
 * Service xử lý các API tin nhắn đến người dùng ẩn danh của Zalo Official Account
 * 
 * Điều kiện sử dụng:
 * - Chỉ có thể gửi tin nhắn đến người dùng ẩn danh trong vòng 48 giờ kể từ lần tương tác cuối cùng
 * - Người dùng ẩn danh là người dùng chưa quan tâm Official Account
 * - Cần có quyền gửi tin nhắn đến người dùng ẩn danh từ Zalo
 */
@Injectable()
export class ZaloAnonymousService {
  private readonly logger = new Logger(ZaloAnonymousService.name);
  private readonly anonymousApiUrl = 'https://openapi.zalo.me/v2.0/oa/message';

  constructor(
    private readonly zaloService: ZaloService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Gửi tin nhắn văn bản đến người dùng ẩn danh
   * @param accessToken Access token của Official Account
   * @param userId ID của người dùng Zalo ẩn danh
   * @param text Nội dung tin nhắn
   * @returns ID của tin nhắn
   */
  async sendAnonymousTextMessage(
    accessToken: string,
    userId: string,
    text: string,
  ): Promise<{ message_id: string }> {
    try {
      const data = {
        recipient: {
          user_id: userId,
        },
        message: {
          text,
        },
      };

      this.logger.debug(`Sending anonymous text message to user ${userId}: ${text}`);
      return await this.zaloService.post<{ message_id: string }>(
        this.anonymousApiUrl,
        accessToken,
        data,
      );
    } catch (error) {
      this.logger.error(`Error sending anonymous text message: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi gửi tin nhắn văn bản đến người dùng ẩn danh',
      );
    }
  }

  /**
   * Gửi tin nhắn ảnh đến người dùng ẩn danh
   * @param accessToken Access token của Official Account
   * @param userId ID của người dùng Zalo ẩn danh
   * @param imageUrl URL của hình ảnh
   * @param message Nội dung tin nhắn kèm theo (nếu có)
   * @returns ID của tin nhắn
   */
  async sendAnonymousImageMessage(
    accessToken: string,
    userId: string,
    imageUrl: string,
    message?: string,
  ): Promise<{ message_id: string }> {
    try {
      const data = {
        recipient: {
          user_id: userId,
        },
        message: {
          attachment: {
            type: 'image',
            payload: {
              url: imageUrl,
              ...(message && { message }),
            },
          },
        },
      };

      this.logger.debug(`Sending anonymous image message to user ${userId}: ${imageUrl}`);
      return await this.zaloService.post<{ message_id: string }>(
        this.anonymousApiUrl,
        accessToken,
        data,
      );
    } catch (error) {
      this.logger.error(`Error sending anonymous image message: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi gửi tin nhắn ảnh đến người dùng ẩn danh',
      );
    }
  }

  /**
   * Gửi tin nhắn file đến người dùng ẩn danh
   * @param accessToken Access token của Official Account
   * @param userId ID của người dùng Zalo ẩn danh
   * @param fileUrl URL của file đính kèm
   * @param filename Tên file
   * @param message Nội dung tin nhắn kèm theo (nếu có)
   * @returns ID của tin nhắn
   */
  async sendAnonymousFileMessage(
    accessToken: string,
    userId: string,
    fileUrl: string,
    filename: string,
    message?: string,
  ): Promise<{ message_id: string }> {
    try {
      const data = {
        recipient: {
          user_id: userId,
        },
        message: {
          attachment: {
            type: 'file',
            payload: {
              url: fileUrl,
              filename: filename,
              ...(message && { message }),
            },
          },
        },
      };

      this.logger.debug(`Sending anonymous file message to user ${userId}: ${filename}`);
      return await this.zaloService.post<{ message_id: string }>(
        this.anonymousApiUrl,
        accessToken,
        data,
      );
    } catch (error) {
      this.logger.error(`Error sending anonymous file message: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi gửi tin nhắn file đến người dùng ẩn danh',
      );
    }
  }

  /**
   * Gửi tin nhắn sticker đến người dùng ẩn danh
   * @param accessToken Access token của Official Account
   * @param userId ID của người dùng Zalo ẩn danh
   * @param stickerId ID của sticker
   * @param message Nội dung tin nhắn kèm theo (nếu có)
   * @returns ID của tin nhắn
   */
  async sendAnonymousStickerMessage(
    accessToken: string,
    userId: string,
    stickerId: string,
    message?: string,
  ): Promise<{ message_id: string }> {
    try {
      const data = {
        recipient: {
          user_id: userId,
        },
        message: {
          attachment: {
            type: 'template',
            payload: {
              template_type: 'sticker',
              sticker_id: stickerId,
              ...(message && { message }),
            },
          },
        },
      };

      this.logger.debug(`Sending anonymous sticker message to user ${userId}: ${stickerId}`);
      return await this.zaloService.post<{ message_id: string }>(
        this.anonymousApiUrl,
        accessToken,
        data,
      );
    } catch (error) {
      this.logger.error(`Error sending anonymous sticker message: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi gửi tin nhắn sticker đến người dùng ẩn danh',
      );
    }
  }

  /**
   * Gửi tin nhắn đến người dùng ẩn danh dựa vào loại tin nhắn
   * @param accessToken Access token của Official Account
   * @param userId ID của người dùng Zalo ẩn danh
   * @param message Tin nhắn cần gửi
   * @returns ID của tin nhắn
   */
  async sendAnonymousMessage(
    accessToken: string,
    userId: string,
    message: ZaloAnonymousMessage,
  ): Promise<{ message_id: string }> {
    try {
      switch (message.type) {
        case 'anonymous_text':
          return await this.sendAnonymousTextMessage(accessToken, userId, message.text);

        case 'anonymous_image':
          return await this.sendAnonymousImageMessage(
            accessToken,
            userId,
            message.url,
            message.message,
          );

        case 'anonymous_file':
          return await this.sendAnonymousFileMessage(
            accessToken,
            userId,
            message.url,
            message.filename,
            message.message,
          );

        case 'anonymous_sticker':
          return await this.sendAnonymousStickerMessage(
            accessToken,
            userId,
            message.sticker_id,
            message.message,
          );

        default:
          throw new AppException(
            ErrorCode.VALIDATION_ERROR,
            'Loại tin nhắn đến người dùng ẩn danh không hợp lệ',
          );
      }
    } catch (error) {
      this.logger.error(`Error sending anonymous message: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi gửi tin nhắn đến người dùng ẩn danh',
      );
    }
  }
}
