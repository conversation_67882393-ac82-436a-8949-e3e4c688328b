import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppException, ErrorCode } from '@common/exceptions';
import { ZaloService } from './zalo.service';
import {
  ZaloQuotaInfo,
  ZaloConversationMessage,
  ZaloConversation,
  ZaloUploadResult,
  ZaloUploadFileInfo,
  ZaloMessagePagination,
} from './zalo.interface';

/**
 * Service xử lý các API quản lý tin nhắn của Zalo Official Account
 * 
 * Điều kiện sử dụng:
 * - Kiểm tra hạn mức: Cần quyền truy cập thông tin quota từ Zalo
 * - Lấy tin nhắn: Chỉ có thể lấy tin nhắn trong vòng 7 ngày gần nhất
 * - Upload file: Kích thước tối đa 25MB, hỗ trợ các định dạng phổ biến
 * - Upload ảnh: <PERSON><PERSON><PERSON> thước tối đa 5MB, định dạng JPG, PNG, GIF
 */
@Injectable()
export class ZaloMessageManagementService {
  private readonly logger = new Logger(ZaloMessageManagementService.name);
  private readonly baseApiUrl = 'https://openapi.zalo.me/v2.0/oa';
  private readonly uploadApiUrl = 'https://openapi.zalo.me/v2.0/oa/upload';

  constructor(
    private readonly zaloService: ZaloService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Kiểm tra hạn mức gửi tin nhắn đến user cụ thể
   * @param accessToken Access token của Official Account
   * @param userId ID của người dùng Zalo
   * @returns Thông tin hạn mức gửi tin nhắn
   */
  async checkMessageQuota(
    accessToken: string,
    userId: string,
  ): Promise<ZaloQuotaInfo> {
    try {
      this.logger.debug(`Checking message quota for user ${userId}`);
      return await this.zaloService.get<ZaloQuotaInfo>(
        `${this.baseApiUrl}/quota?user_id=${userId}`,
        accessToken,
      );
    } catch (error) {
      this.logger.error(`Error checking message quota: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi kiểm tra hạn mức gửi tin nhắn',
      );
    }
  }

  /**
   * Lấy thông tin tin nhắn gần nhất
   * @param accessToken Access token của Official Account
   * @param userId ID của người dùng Zalo (tùy chọn)
   * @param count Số lượng tin nhắn tối đa trả về (1-100)
   * @returns Danh sách tin nhắn gần nhất
   */
  async getRecentMessages(
    accessToken: string,
    userId?: string,
    count: number = 20,
  ): Promise<{ messages: ZaloConversationMessage[]; total: number }> {
    try {
      const params = new URLSearchParams();
      if (userId) params.append('user_id', userId);
      params.append('count', Math.min(Math.max(count, 1), 100).toString());

      this.logger.debug(`Getting recent messages with params: ${params.toString()}`);
      return await this.zaloService.get<{ messages: ZaloConversationMessage[]; total: number }>(
        `${this.baseApiUrl}/conversation/message?${params.toString()}`,
        accessToken,
      );
    } catch (error) {
      this.logger.error(`Error getting recent messages: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy thông tin tin nhắn gần nhất',
      );
    }
  }

  /**
   * Lấy thông tin tin nhắn trong một hội thoại
   * @param accessToken Access token của Official Account
   * @param userId ID của người dùng Zalo
   * @param pagination Tham số phân trang
   * @returns Danh sách tin nhắn trong hội thoại
   */
  async getConversationMessages(
    accessToken: string,
    userId: string,
    pagination?: ZaloMessagePagination,
  ): Promise<{ messages: ZaloConversationMessage[]; has_more: boolean }> {
    try {
      const params = new URLSearchParams();
      params.append('user_id', userId);
      
      if (pagination?.count) {
        params.append('count', Math.min(Math.max(pagination.count, 1), 100).toString());
      }
      if (pagination?.offset) {
        params.append('offset', pagination.offset.toString());
      }
      if (pagination?.before) {
        params.append('before', pagination.before);
      }
      if (pagination?.after) {
        params.append('after', pagination.after);
      }

      this.logger.debug(`Getting conversation messages for user ${userId}`);
      return await this.zaloService.get<{ messages: ZaloConversationMessage[]; has_more: boolean }>(
        `${this.baseApiUrl}/conversation/message?${params.toString()}`,
        accessToken,
      );
    } catch (error) {
      this.logger.error(`Error getting conversation messages: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy thông tin tin nhắn trong hội thoại',
      );
    }
  }

  /**
   * Upload hình ảnh
   * @param accessToken Access token của Official Account
   * @param fileInfo Thông tin file ảnh
   * @returns Kết quả upload
   */
  async uploadImage(
    accessToken: string,
    fileInfo: ZaloUploadFileInfo,
  ): Promise<ZaloUploadResult> {
    try {
      // Kiểm tra định dạng file ảnh
      const allowedImageTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
      if (!allowedImageTypes.includes(fileInfo.mimetype)) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Định dạng file không được hỗ trợ. Chỉ hỗ trợ JPG, PNG, GIF',
        );
      }

      // Kiểm tra kích thước file (5MB)
      const maxSize = 5 * 1024 * 1024; // 5MB
      if (fileInfo.size > maxSize) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Kích thước file vượt quá 5MB',
        );
      }

      const formData = new FormData();
      const blob = fileInfo.data instanceof Buffer 
        ? new Blob([fileInfo.data], { type: fileInfo.mimetype })
        : new Blob([Buffer.from(typeof fileInfo.data === 'string' ? fileInfo.data : '', 'base64')], { type: fileInfo.mimetype });
      
      formData.append('file', blob, fileInfo.filename);

      this.logger.debug(`Uploading image: ${fileInfo.filename}`);
      return await this.zaloService.postFormData<ZaloUploadResult>(
        `${this.uploadApiUrl}/image`,
        accessToken,
        formData,
      );
    } catch (error) {
      this.logger.error(`Error uploading image: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi upload hình ảnh',
      );
    }
  }

  /**
   * Upload file
   * @param accessToken Access token của Official Account
   * @param fileInfo Thông tin file
   * @returns Kết quả upload
   */
  async uploadFile(
    accessToken: string,
    fileInfo: ZaloUploadFileInfo,
  ): Promise<ZaloUploadResult> {
    try {
      // Kiểm tra kích thước file (25MB)
      const maxSize = 25 * 1024 * 1024; // 25MB
      if (fileInfo.size > maxSize) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Kích thước file vượt quá 25MB',
        );
      }

      // Kiểm tra định dạng file được hỗ trợ
      const allowedFileTypes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-powerpoint',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'text/plain',
        'application/zip',
        'application/x-rar-compressed',
      ];

      if (!allowedFileTypes.includes(fileInfo.mimetype)) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Định dạng file không được hỗ trợ',
        );
      }

      const formData = new FormData();
      const blob = fileInfo.data instanceof Buffer 
        ? new Blob([fileInfo.data], { type: fileInfo.mimetype })
        : new Blob([Buffer.from(typeof fileInfo.data === 'string' ? fileInfo.data : '', 'base64')], { type: fileInfo.mimetype });
      
      formData.append('file', blob, fileInfo.filename);

      this.logger.debug(`Uploading file: ${fileInfo.filename}`);
      return await this.zaloService.postFormData<ZaloUploadResult>(
        `${this.uploadApiUrl}/file`,
        accessToken,
        formData,
      );
    } catch (error) {
      this.logger.error(`Error uploading file: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi upload file',
      );
    }
  }

  /**
   * Upload ảnh GIF
   * @param accessToken Access token của Official Account
   * @param fileInfo Thông tin file GIF
   * @returns Kết quả upload
   */
  async uploadGif(
    accessToken: string,
    fileInfo: ZaloUploadFileInfo,
  ): Promise<ZaloUploadResult> {
    try {
      // Kiểm tra định dạng file GIF
      if (fileInfo.mimetype !== 'image/gif') {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'File phải có định dạng GIF',
        );
      }

      // Kiểm tra kích thước file (5MB)
      const maxSize = 5 * 1024 * 1024; // 5MB
      if (fileInfo.size > maxSize) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Kích thước file GIF vượt quá 5MB',
        );
      }

      const formData = new FormData();
      const blob = fileInfo.data instanceof Buffer 
        ? new Blob([fileInfo.data], { type: fileInfo.mimetype })
        : new Blob([Buffer.from(typeof fileInfo.data === 'string' ? fileInfo.data : '', 'base64')], { type: fileInfo.mimetype });
      
      formData.append('file', blob, fileInfo.filename);

      this.logger.debug(`Uploading GIF: ${fileInfo.filename}`);
      return await this.zaloService.postFormData<ZaloUploadResult>(
        `${this.uploadApiUrl}/gif`,
        accessToken,
        formData,
      );
    } catch (error) {
      this.logger.error(`Error uploading GIF: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi upload ảnh GIF',
      );
    }
  }

  /**
   * Lấy danh sách hội thoại
   * @param accessToken Access token của Official Account
   * @param count Số lượng hội thoại tối đa trả về (1-50)
   * @param offset Offset để phân trang
   * @returns Danh sách hội thoại
   */
  async getConversations(
    accessToken: string,
    count: number = 20,
    offset: number = 0,
  ): Promise<{ conversations: ZaloConversation[]; total: number }> {
    try {
      const params = new URLSearchParams();
      params.append('count', Math.min(Math.max(count, 1), 50).toString());
      params.append('offset', offset.toString());

      this.logger.debug(`Getting conversations with count: ${count}, offset: ${offset}`);
      return await this.zaloService.get<{ conversations: ZaloConversation[]; total: number }>(
        `${this.baseApiUrl}/conversation?${params.toString()}`,
        accessToken,
      );
    } catch (error) {
      this.logger.error(`Error getting conversations: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy danh sách hội thoại',
      );
    }
  }

  /**
   * Đánh dấu tin nhắn đã đọc
   * @param accessToken Access token của Official Account
   * @param userId ID của người dùng Zalo
   * @param messageId ID của tin nhắn (tùy chọn, nếu không có sẽ đánh dấu tất cả)
   * @returns Kết quả đánh dấu đã đọc
   */
  async markAsRead(
    accessToken: string,
    userId: string,
    messageId?: string,
  ): Promise<{ success: boolean }> {
    try {
      const data = {
        user_id: userId,
        ...(messageId && { message_id: messageId }),
      };

      this.logger.debug(`Marking messages as read for user ${userId}`);
      return await this.zaloService.post<{ success: boolean }>(
        `${this.baseApiUrl}/conversation/read`,
        accessToken,
        data,
      );
    } catch (error) {
      this.logger.error(`Error marking messages as read: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi đánh dấu tin nhắn đã đọc',
      );
    }
  }

  /**
   * Lấy thông tin chi tiết một tin nhắn
   * @param accessToken Access token của Official Account
   * @param messageId ID của tin nhắn
   * @returns Thông tin chi tiết tin nhắn
   */
  async getMessageDetail(
    accessToken: string,
    messageId: string,
  ): Promise<ZaloConversationMessage> {
    try {
      this.logger.debug(`Getting message detail for message ${messageId}`);
      return await this.zaloService.get<ZaloConversationMessage>(
        `${this.baseApiUrl}/message/${messageId}`,
        accessToken,
      );
    } catch (error) {
      this.logger.error(`Error getting message detail: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy thông tin chi tiết tin nhắn',
      );
    }
  }

  /**
   * Xóa tin nhắn
   * @param accessToken Access token của Official Account
   * @param messageId ID của tin nhắn
   * @returns Kết quả xóa tin nhắn
   */
  async deleteMessage(
    accessToken: string,
    messageId: string,
  ): Promise<{ success: boolean }> {
    try {
      this.logger.debug(`Deleting message ${messageId}`);
      return await this.zaloService.post<{ success: boolean }>(
        `${this.baseApiUrl}/message/${messageId}/delete`,
        accessToken,
      );
    } catch (error) {
      this.logger.error(`Error deleting message: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi xóa tin nhắn',
      );
    }
  }

  /**
   * Tìm kiếm tin nhắn
   * @param accessToken Access token của Official Account
   * @param query Từ khóa tìm kiếm
   * @param userId ID của người dùng (tùy chọn)
   * @param pagination Tham số phân trang
   * @returns Kết quả tìm kiếm
   */
  async searchMessages(
    accessToken: string,
    query: string,
    userId?: string,
    pagination?: ZaloMessagePagination,
  ): Promise<{ messages: ZaloConversationMessage[]; total: number }> {
    try {
      const params = new URLSearchParams();
      params.append('q', query);
      if (userId) params.append('user_id', userId);
      if (pagination?.count) {
        params.append('count', Math.min(Math.max(pagination.count, 1), 100).toString());
      }
      if (pagination?.offset) {
        params.append('offset', pagination.offset.toString());
      }

      this.logger.debug(`Searching messages with query: ${query}`);
      return await this.zaloService.get<{ messages: ZaloConversationMessage[]; total: number }>(
        `${this.baseApiUrl}/message/search?${params.toString()}`,
        accessToken,
      );
    } catch (error) {
      this.logger.error(`Error searching messages: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi tìm kiếm tin nhắn',
      );
    }
  }

  /**
   * Lấy thống kê tin nhắn
   * @param accessToken Access token của Official Account
   * @param fromDate Ngày bắt đầu (YYYY-MM-DD)
   * @param toDate Ngày kết thúc (YYYY-MM-DD)
   * @returns Thống kê tin nhắn
   */
  async getMessageStatistics(
    accessToken: string,
    fromDate: string,
    toDate: string,
  ): Promise<{
    total_sent: number;
    total_received: number;
    total_conversations: number;
    daily_stats: Array<{
      date: string;
      sent: number;
      received: number;
    }>;
  }> {
    try {
      const params = new URLSearchParams();
      params.append('from_date', fromDate);
      params.append('to_date', toDate);

      this.logger.debug(`Getting message statistics from ${fromDate} to ${toDate}`);
      return await this.zaloService.get<{
        total_sent: number;
        total_received: number;
        total_conversations: number;
        daily_stats: Array<{
          date: string;
          sent: number;
          received: number;
        }>;
      }>(
        `${this.baseApiUrl}/message/statistics?${params.toString()}`,
        accessToken,
      );
    } catch (error) {
      this.logger.error(`Error getting message statistics: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy thống kê tin nhắn',
      );
    }
  }
}
