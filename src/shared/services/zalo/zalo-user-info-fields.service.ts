import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppException, ErrorCode } from '@common/exceptions';
import { ZaloService } from './zalo.service';
import {
  ZaloUserInfoField,
  ZaloUserInfoFieldList,
  ZaloCreateUserInfoFieldRequest,
  ZaloUpdateUserInfoFieldRequest,
} from './zalo.interface';

/**
 * Service xử lý các API quản lý trường thông tin người dùng của Zalo Official Account
 * 
 * Điều kiện sử dụng từ tài liệu Zalo:
 * - L<PERSON>y danh sách trường thông tin: Cần quyền đọc thông tin người dùng
 * - Tạo mới trường thông tin tùy biến: Cần quyền quản lý người dùng, tối đa 50 trường tùy biến
 * - C<PERSON><PERSON> nhật trường thông tin: Cần quyền quản lý người dùng, chỉ có thể cập nhật trường tùy biến
 * - Xóa trường thông tin tùy biến: Cần quyền quản lý người dùng, chỉ có thể xóa trường tùy biến
 * 
 * Lưu ý:
 * - Trường thông tin mặc định của hệ thống không thể chỉnh sửa hoặc xóa
 * - Trường thông tin tùy biến có thể có các kiểu dữ liệu: text, number, date, select
 * - Mỗi Official Account có thể tạo tối đa 50 trường thông tin tùy biến
 * - Khi xóa trường thông tin, dữ liệu của trường đó sẽ bị xóa khỏi tất cả người dùng
 */
@Injectable()
export class ZaloUserInfoFieldsService {
  private readonly logger = new Logger(ZaloUserInfoFieldsService.name);
  private readonly baseApiUrl = 'https://openapi.zalo.me/v2.0/oa';

  constructor(
    private readonly zaloService: ZaloService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Lấy danh sách tất cả trường thông tin (bao gồm trường mặc định và tùy biến)
   * @param accessToken Access token của Official Account
   * @param offset Vị trí bắt đầu (mặc định: 0)
   * @param count Số lượng trường tối đa trả về (1-50, mặc định: 20)
   * @returns Danh sách trường thông tin
   */
  async getUserInfoFields(
    accessToken: string,
    offset: number = 0,
    count: number = 20,
  ): Promise<ZaloUserInfoFieldList> {
    try {
      const params = new URLSearchParams();
      params.append('offset', offset.toString());
      params.append('count', Math.min(Math.max(count, 1), 50).toString());

      this.logger.debug(`Getting user info fields with offset: ${offset}, count: ${count}`);
      return await this.zaloService.get<ZaloUserInfoFieldList>(
        `${this.baseApiUrl}/user/field?${params.toString()}`,
        accessToken,
      );
    } catch (error) {
      this.logger.error(`Error getting user info fields: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy danh sách trường thông tin người dùng',
      );
    }
  }

  /**
   * Tạo mới trường thông tin tùy biến
   * @param accessToken Access token của Official Account
   * @param fieldData Thông tin trường cần tạo
   * @returns Thông tin trường đã tạo
   */
  async createUserInfoField(
    accessToken: string,
    fieldData: ZaloCreateUserInfoFieldRequest,
  ): Promise<ZaloUserInfoField> {
    try {
      this.logger.debug(`Creating new user info field: ${fieldData.field_name}`);
      return await this.zaloService.post<ZaloUserInfoField>(
        `${this.baseApiUrl}/user/field/create`,
        accessToken,
        fieldData,
      );
    } catch (error) {
      this.logger.error(`Error creating user info field: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi tạo trường thông tin tùy biến',
      );
    }
  }

  /**
   * Cập nhật trường thông tin tùy biến
   * @param accessToken Access token của Official Account
   * @param fieldId ID của trường thông tin cần cập nhật
   * @param updateData Dữ liệu cập nhật
   * @returns Thông tin trường đã cập nhật
   */
  async updateUserInfoField(
    accessToken: string,
    fieldId: string,
    updateData: ZaloUpdateUserInfoFieldRequest,
  ): Promise<ZaloUserInfoField> {
    try {
      this.logger.debug(`Updating user info field ${fieldId}`);
      return await this.zaloService.post<ZaloUserInfoField>(
        `${this.baseApiUrl}/user/field/${fieldId}/update`,
        accessToken,
        updateData,
      );
    } catch (error) {
      this.logger.error(`Error updating user info field: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi cập nhật trường thông tin',
      );
    }
  }

  /**
   * Xóa trường thông tin tùy biến
   * @param accessToken Access token của Official Account
   * @param fieldId ID của trường thông tin cần xóa
   * @returns Kết quả xóa
   */
  async deleteUserInfoField(
    accessToken: string,
    fieldId: string,
  ): Promise<{ success: boolean }> {
    try {
      this.logger.debug(`Deleting user info field ${fieldId}`);
      return await this.zaloService.post<{ success: boolean }>(
        `${this.baseApiUrl}/user/field/${fieldId}/delete`,
        accessToken,
      );
    } catch (error) {
      this.logger.error(`Error deleting user info field: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi xóa trường thông tin tùy biến',
      );
    }
  }

  /**
   * Lấy chi tiết một trường thông tin cụ thể
   * @param accessToken Access token của Official Account
   * @param fieldId ID của trường thông tin
   * @returns Chi tiết trường thông tin
   */
  async getUserInfoFieldDetail(
    accessToken: string,
    fieldId: string,
  ): Promise<ZaloUserInfoField> {
    try {
      this.logger.debug(`Getting user info field detail for field ${fieldId}`);
      return await this.zaloService.get<ZaloUserInfoField>(
        `${this.baseApiUrl}/user/field/${fieldId}`,
        accessToken,
      );
    } catch (error) {
      this.logger.error(`Error getting user info field detail: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy chi tiết trường thông tin',
      );
    }
  }
}
