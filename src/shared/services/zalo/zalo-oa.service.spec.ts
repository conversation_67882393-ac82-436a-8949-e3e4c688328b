import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { ZaloOaService } from './zalo-oa.service';
import { ZaloService } from './zalo.service';
import { AppException, ErrorCode } from '@common/exceptions';

describe('ZaloOaService', () => {
  let service: ZaloOaService;
  let zaloService: jest.Mocked<ZaloService>;
  let configService: jest.Mocked<ConfigService>;

  beforeEach(async () => {
    const mockZaloService = {
      get: jest.fn(),
      post: jest.fn(),
    };

    const mockConfigService = {
      get: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ZaloOaService,
        {
          provide: ZaloService,
          useValue: mockZaloService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<ZaloOaService>(ZaloOaService);
    zaloService = module.get(ZaloService);
    configService = module.get(ConfigService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getMessageQuota', () => {
    it('should return message quota successfully', async () => {
      const mockQuota = {
        daily_quota: 1000,
        remaining_quota: 500,
        quota_type: 'free',
        reset_time: **********,
      };

      zaloService.get.mockResolvedValue(mockQuota);

      const result = await service.getMessageQuota('test-access-token');

      expect(result).toEqual(mockQuota);
      expect(zaloService.get).toHaveBeenCalledWith(
        'https://openapi.zalo.me/v2.0/oa/quota',
        'test-access-token',
      );
    });

    it('should throw AppException when API call fails', async () => {
      zaloService.get.mockRejectedValue(new Error('API Error'));

      await expect(service.getMessageQuota('test-access-token')).rejects.toThrow(
        AppException,
      );
    });
  });

  describe('getDetailedOaInfo', () => {
    it('should return detailed OA info successfully', async () => {
      const mockOaInfo = {
        oa_id: '*********',
        name: 'Test OA',
        description: 'Test Official Account',
        avatar: 'https://example.com/avatar.jpg',
        status: 'active',
        num_follower: 1000,
        oa_type: 'business',
        is_verified: true,
        contact_info: {
          address: '123 Test Street',
          phone: '+***********',
          email: '<EMAIL>',
          website: 'https://example.com',
        },
        zca_info: {
          is_linked: true,
          balance: 100000,
        },
        created_time: **********,
      };

      zaloService.get.mockResolvedValue(mockOaInfo);

      const result = await service.getDetailedOaInfo('test-access-token');

      expect(result).toEqual(mockOaInfo);
      expect(zaloService.get).toHaveBeenCalledWith(
        'https://openapi.zalo.me/v2.0/oa/getoa',
        'test-access-token',
      );
    });

    it('should throw AppException when API call fails', async () => {
      zaloService.get.mockRejectedValue(new Error('API Error'));

      await expect(service.getDetailedOaInfo('test-access-token')).rejects.toThrow(
        AppException,
      );
    });
  });

  describe('getOaInfo', () => {
    it('should return basic OA info successfully', async () => {
      const mockOaInfo = {
        oa_id: '*********',
        name: 'Test OA',
        description: 'Test Official Account',
        avatar: 'https://example.com/avatar.jpg',
        status: 'active',
      };

      zaloService.get.mockResolvedValue(mockOaInfo);

      const result = await service.getOaInfo('test-access-token');

      expect(result).toEqual(mockOaInfo);
      expect(zaloService.get).toHaveBeenCalledWith(
        'https://openapi.zalo.me/v2.0/oa/getoa',
        'test-access-token',
      );
    });
  });
});
