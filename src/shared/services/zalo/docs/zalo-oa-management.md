# Zalo Official Account Management APIs

Tài liệu này mô tả các API để quản lý thông tin và hạn mức tin nhắn của Zalo Official Account.

## Điều kiện sử dụng

- Access token hợp lệ của Official Account
- Official Account phải được kích hoạt và có quyền sử dụng API

## API Endpoints

### 1. Kiểm tra hạn mức tin nhắn OA

**Endpoint:** `GET https://openapi.zalo.me/v2.0/oa/quota`

**Mô tả:** Lấy thông tin về hạn mức tin nhắn hàng ngày của Official Account.

**Headers:**
```
access_token: {access_token}
```

**Response:**
```typescript
interface ZaloMessageQuota {
  daily_quota: number;        // Hạn mức tin nhắn hàng ngày
  remaining_quota: number;    // Số tin nhắn còn lại trong ngày
  quota_type: string;         // Loại quota ("free", "paid")
  reset_time: number;         // Thời gian reset quota (Unix timestamp)
}
```

**Ví dụ sử dụng:**
```typescript
import { ZaloOaService } from '@/shared/services/zalo';

const zaloOaService = new ZaloOaService();
const quota = await zaloOaService.getMessageQuota(accessToken);

console.log(`Còn lại: ${quota.remaining_quota}/${quota.daily_quota} tin nhắn`);
```

### 2. Lấy thông tin chi tiết Official Account

**Endpoint:** `GET https://openapi.zalo.me/v2.0/oa/getoa`

**Mô tả:** Lấy thông tin chi tiết về Official Account bao gồm số lượng người theo dõi, thông tin liên hệ, và trạng thái ví ZCA.

**Headers:**
```
access_token: {access_token}
```

**Response:**
```typescript
interface ZaloDetailedOaInfo {
  oa_id: string;              // ID của Official Account
  name: string;               // Tên Official Account
  description: string;        // Mô tả
  avatar: string;             // URL avatar
  status: string;             // Trạng thái
  num_follower: number;       // Số lượng người theo dõi
  oa_type: string;            // Loại OA
  is_verified: boolean;       // Trạng thái xác thực
  contact_info?: {            // Thông tin liên hệ
    address?: string;
    phone?: string;
    email?: string;
    website?: string;
  };
  zca_info?: {               // Thông tin ví ZCA
    is_linked: boolean;
    balance?: number;
  };
  created_time?: number;     // Thời gian tạo (Unix timestamp)
}
```

**Ví dụ sử dụng:**
```typescript
import { ZaloOaService } from '@/shared/services/zalo';

const zaloOaService = new ZaloOaService();
const oaInfo = await zaloOaService.getDetailedOaInfo(accessToken);

console.log(`OA: ${oaInfo.name} - Followers: ${oaInfo.num_follower}`);
if (oaInfo.zca_info?.is_linked) {
  console.log(`Số dư ví: ${oaInfo.zca_info.balance} VND`);
}
```

## Xử lý lỗi

Tất cả các API đều có xử lý lỗi thống nhất:

```typescript
try {
  const result = await zaloOaService.getMessageQuota(accessToken);
} catch (error) {
  if (error instanceof AppException) {
    console.error('Lỗi API:', error.message);
  } else {
    console.error('Lỗi không xác định:', error);
  }
}
```

## Lưu ý quan trọng

1. **Rate Limiting:** Zalo có giới hạn số lượng request API, hãy sử dụng hợp lý
2. **Access Token:** Đảm bảo access token luôn hợp lệ và không bị hết hạn
3. **Quota Management:** Kiểm tra quota trước khi gửi tin nhắn để tránh lỗi
4. **ZCA Wallet:** Thông tin ví ZCA chỉ có khi OA đã liên kết ví

## Tham khảo

- [Kiểm tra hạn mức tin nhắn OA](https://developers.zalo.me/docs/official-account/quan-ly/quan-ly-thong-tin-oa/kiem-tra-han-muc-tin-nhan-oa)
- [Lấy thông tin Zalo Official Account](https://developers.zalo.me/docs/official-account/quan-ly/quan-ly-thong-tin-oa/lay-thong-tin-zalo-official-account)
