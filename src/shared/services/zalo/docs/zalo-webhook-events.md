# Zalo Webhook Events Documentation

Tài liệu này mô tả các sự kiện webhook từ Zalo Official Account và cách xử lý chúng.

## Tổng quan

Zalo webhook cho phép ứng dụng nhận thông báo real-time về các sự kiện xảy ra trên Official Account như:
- Người dùng gửi tin nhắn
- Người dùng tương tác với tin nhắn (reaction, seen)
- Người dùng theo dõi/hủy theo dõi OA
- OA gửi tin nhắn cho người dùng

## Cấu trúc Webhook Event

Tất cả webhook events đều có cấu trúc cơ bản:

```typescript
interface ZaloWebhookBaseEvent {
  event_id: string;        // ID duy nhất của sự kiện
  event_name: string;      // Tên sự kiện
  timestamp: number;       // Thời gian x<PERSON> ra (Unix timestamp)
  oa_id: string;          // ID của Official Account
}
```

## C<PERSON>c loại sự kiện

### 1. Sự kiện tin nhắn từ người dùng

#### 1.1 Tin nhắn văn bản (`user_send_text`)
```typescript
interface ZaloUserSendTextEvent {
  event_name: 'user_send_text';
  data: {
    sender: ZaloWebhookSender;
    recipient: ZaloWebhookRecipient;
    message: {
      msg_id: string;
      text: string;
      timestamp: number;
    };
  };
}
```

#### 1.2 Tin nhắn hình ảnh (`user_send_image`)
```typescript
interface ZaloUserSendImageEvent {
  event_name: 'user_send_image';
  data: {
    sender: ZaloWebhookSender;
    recipient: ZaloWebhookRecipient;
    message: {
      msg_id: string;
      attachments: ZaloWebhookAttachment[];
      timestamp: number;
    };
  };
}
```

#### 1.3 Các loại tin nhắn khác
- `user_send_file` - File đính kèm
- `user_send_sticker` - Sticker
- `user_send_gif` - GIF
- `user_send_audio` - Audio
- `user_send_video` - Video
- `user_send_location` - Vị trí

### 2. Sự kiện tương tác

#### 2.1 Click nút "Nhắn tin" (`user_click_chatnow`)
```typescript
interface ZaloUserClickChatNowEvent {
  event_name: 'user_click_chatnow';
  data: {
    sender: ZaloWebhookSender;
    recipient: ZaloWebhookRecipient;
    timestamp: number;
  };
}
```

#### 2.2 Thả biểu tượng cảm xúc (`user_reaction`)
```typescript
interface ZaloUserReactionEvent {
  event_name: 'user_reaction';
  data: {
    sender: ZaloWebhookSender;
    recipient: ZaloWebhookRecipient;
    reaction: {
      msg_id: string;
      reaction: string;
      action: 'add' | 'remove';
    };
    timestamp: number;
  };
}
```

#### 2.3 Đã xem tin nhắn (`user_seen_message`)
```typescript
interface ZaloUserSeenMessageEvent {
  event_name: 'user_seen_message';
  data: {
    sender: ZaloWebhookSender;
    recipient: ZaloWebhookRecipient;
    message: {
      msg_id: string;
      timestamp: number;
    };
    seen_timestamp: number;
  };
}
```

### 3. Sự kiện theo dõi

#### 3.1 Theo dõi OA (`user_follow`)
```typescript
interface ZaloUserFollowEvent {
  event_name: 'user_follow';
  data: {
    follower: ZaloWebhookSender;
    timestamp: number;
  };
}
```

#### 3.2 Hủy theo dõi OA (`user_unfollow`)
```typescript
interface ZaloUserUnfollowEvent {
  event_name: 'user_unfollow';
  data: {
    follower: ZaloWebhookSender;
    timestamp: number;
  };
}
```

## Type Guards và Utilities

### Type Guards
```typescript
// Kiểm tra loại sự kiện
isUserMessageEvent(event: ZaloWebhookEvent): event is ZaloUserMessageEvent
isInteractionEvent(event: ZaloWebhookEvent): event is ZaloInteractionEvent
isOaMessageEvent(event: ZaloWebhookEvent): event is ZaloOaMessageEvent
isFollowEvent(event: ZaloWebhookEvent): event is ZaloFollowEvent
```

### Enum các loại sự kiện
```typescript
enum ZaloWebhookEventType {
  USER_SEND_TEXT = 'user_send_text',
  USER_SEND_IMAGE = 'user_send_image',
  USER_SEND_FILE = 'user_send_file',
  // ... các loại khác
}
```

## Xử lý Webhook

### 1. Xác thực Webhook
```typescript
const isValid = zaloWebhookService.verifyWebhook(timestamp, mac, body);
if (!isValid) {
  throw new Error('Invalid webhook signature');
}
```

### 2. Xử lý sự kiện
```typescript
await zaloWebhookService.processWebhookEvent(event);
```

### 3. Ví dụ xử lý cụ thể
```typescript
if (isUserMessageEvent(event)) {
  switch (event.event_name) {
    case ZaloWebhookEventType.USER_SEND_TEXT:
      // Xử lý tin nhắn văn bản
      const text = event.data.message.text;
      const userId = event.data.sender.id;
      await handleTextMessage(userId, text);
      break;
      
    case ZaloWebhookEventType.USER_SEND_IMAGE:
      // Xử lý tin nhắn hình ảnh
      const imageUrl = event.data.message.attachments[0].payload.url;
      await handleImageMessage(userId, imageUrl);
      break;
  }
}
```

## Best Practices

1. **Xác thực webhook**: Luôn xác thực signature trước khi xử lý
2. **Idempotency**: Xử lý trùng lặp event_id
3. **Error handling**: Xử lý lỗi gracefully và log chi tiết
4. **Async processing**: Xử lý webhook async để tránh timeout
5. **Rate limiting**: Respect rate limits khi gọi API response

## Tham khảo

- [Tổng quan Webhook](https://developers.zalo.me/docs/official-account/webhook/tong-quan)
- [Sự kiện tin nhắn](https://developers.zalo.me/docs/official-account/webhook/tin-nhan/)
- [Xác thực Webhook](https://developers.zalo.me/docs/official-account/webhook/xac-thuc-webhook)
