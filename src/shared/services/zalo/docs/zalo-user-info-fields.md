# Zalo User Info Fields Service

Service xử lý các API quản lý trường thông tin người dùng của Zalo Official Account.

## Tài liệu tham khảo

- [L<PERSON>y danh sách trường thông tin](https://developers.zalo.me/docs/official-account/quan-ly/quan-ly-truong-thong-tin-nguoi-dung/lay-danh-sach-truong-thong-tin)
- [Tạo mới trường thông tin tùy biến](https://developers.zalo.me/docs/official-account/quan-ly/quan-ly-truong-thong-tin-nguoi-dung/tao-moi-truong-thong-tin-tuy-bien)
- [Cập nhật trường thông tin](https://developers.zalo.me/docs/official-account/quan-ly/quan-ly-truong-thong-tin-nguoi-dung/cap-nhat-truong-thong-tin)
- [<PERSON><PERSON><PERSON> trường thông tin tùy biến](https://developers.zalo.me/docs/official-account/quan-ly/quan-ly-truong-thong-tin-nguoi-dung/xoa-truong-thong-tin-tuy-bien)

## Điều kiện sử dụng

### Quyền truy cập
- **Lấy danh sách trường thông tin**: Cần quyền đọc thông tin người dùng
- **Tạo mới trường thông tin tùy biến**: Cần quyền quản lý người dùng
- **Cập nhật trường thông tin**: Cần quyền quản lý người dùng
- **Xóa trường thông tin tùy biến**: Cần quyền quản lý người dùng

### Giới hạn
- Mỗi Official Account có thể tạo tối đa **50 trường thông tin tùy biến**
- Chỉ có thể chỉnh sửa hoặc xóa **trường thông tin tùy biến**, không thể thao tác với trường hệ thống
- Khi xóa trường thông tin, dữ liệu của trường đó sẽ bị xóa khỏi **tất cả người dùng**

## Các kiểu dữ liệu trường thông tin

| Kiểu | Mô tả | Ví dụ |
|------|-------|-------|
| `text` | Văn bản | Tên công ty, địa chỉ |
| `number` | Số | Tuổi, lương |
| `date` | Ngày tháng | Ngày sinh, ngày gia nhập |
| `select` | Lựa chọn | Giới tính, trình độ học vấn |

## API Methods

### 1. getUserInfoFields()
Lấy danh sách tất cả trường thông tin (bao gồm trường mặc định và tùy biến).

```typescript
const fields = await zaloUserInfoFieldsService.getUserInfoFields(
  accessToken,
  0,    // offset
  20    // count
);
```

**Tham số:**
- `accessToken`: Access token của Official Account
- `offset`: Vị trí bắt đầu (mặc định: 0)
- `count`: Số lượng trường tối đa trả về (1-50, mặc định: 20)

### 2. createUserInfoField()
Tạo mới trường thông tin tùy biến.

```typescript
const newField = await zaloUserInfoFieldsService.createUserInfoField(
  accessToken,
  {
    field_name: 'Công ty',
    field_type: ZaloUserInfoFieldType.TEXT,
    description: 'Tên công ty nơi khách hàng làm việc',
    required: false,
    default_value: 'Không xác định',
    display_order: 1
  }
);
```

**Tham số:**
- `field_name`: Tên trường thông tin (bắt buộc)
- `field_type`: Kiểu dữ liệu (text, number, date, select)
- `description`: Mô tả trường thông tin (tùy chọn)
- `required`: Trường có bắt buộc không (tùy chọn)
- `options`: Danh sách tùy chọn cho kiểu select (tùy chọn)
- `default_value`: Giá trị mặc định (tùy chọn)
- `display_order`: Thứ tự hiển thị (tùy chọn)

### 3. updateUserInfoField()
Cập nhật trường thông tin tùy biến.

```typescript
const updatedField = await zaloUserInfoFieldsService.updateUserInfoField(
  accessToken,
  fieldId,
  {
    field_name: 'Công ty làm việc',
    description: 'Tên công ty nơi khách hàng đang làm việc',
    required: true
  }
);
```

### 4. deleteUserInfoField()
Xóa trường thông tin tùy biến.

```typescript
const result = await zaloUserInfoFieldsService.deleteUserInfoField(
  accessToken,
  fieldId
);
```

### 5. getUserInfoFieldDetail()
Lấy chi tiết một trường thông tin cụ thể.

```typescript
const fieldDetail = await zaloUserInfoFieldsService.getUserInfoFieldDetail(
  accessToken,
  fieldId
);
```

## Ví dụ sử dụng

### Tạo trường thông tin kiểu select

```typescript
const selectField = await zaloUserInfoFieldsService.createUserInfoField(
  accessToken,
  {
    field_name: 'Trình độ học vấn',
    field_type: ZaloUserInfoFieldType.SELECT,
    description: 'Trình độ học vấn của khách hàng',
    required: false,
    options: [
      { value: 'high_school', label: 'Trung học phổ thông' },
      { value: 'college', label: 'Cao đẳng' },
      { value: 'university', label: 'Đại học' },
      { value: 'master', label: 'Thạc sĩ' },
      { value: 'phd', label: 'Tiến sĩ' }
    ],
    default_value: 'university',
    display_order: 2
  }
);
```

### Lấy danh sách và lọc trường tùy biến

```typescript
const allFields = await zaloUserInfoFieldsService.getUserInfoFields(accessToken);
const customFields = allFields.fields.filter(field => !field.is_system_field);
console.log(`Có ${customFields.length} trường tùy biến`);
```

## Xử lý lỗi

Service sẽ throw `AppException` với `ErrorCode.EXTERNAL_SERVICE_ERROR` khi có lỗi từ Zalo API.

```typescript
try {
  const fields = await zaloUserInfoFieldsService.getUserInfoFields(accessToken);
} catch (error) {
  if (error instanceof AppException) {
    console.error('Lỗi Zalo API:', error.message);
  }
}
```
