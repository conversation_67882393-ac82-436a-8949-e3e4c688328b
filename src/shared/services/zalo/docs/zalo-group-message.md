# Zalo Group Message Framework (GMF) Service

## Tổng quan

`ZaloGroupMessageService` là service xử lý các API tin nhắn nhóm của Zalo Official Account thông qua Group Message Framework (GMF). Service này cho phép gửi các loại tin nhắn khác nhau đến nhóm chat và quản lý thông tin nhóm.

## Điều kiện sử dụng

### 1. Điều kiện cần có để gửi tin nhắn nhóm (Opt-in condition)
- OA phải có được `group_id` của nhóm chat
- OA phải được thêm vào nhóm chat bởi admin nhóm
- OA phải có quyền gửi tin nhắn trong nhóm (đư<PERSON><PERSON> cấp bởi admin nhóm)
- Nhóm chat phải đang hoạt động (không bị khóa hoặc giải tán)

### 2. Quyền truy cập
- Ứng dụng cần đư<PERSON><PERSON> cấp quyền quản lý thông tin nhóm chat
- Access token phải có scope "manage_group" hoặc tương đương
- OA phải được xác thực và có trạng thái hoạt động

### 3. Giới hạn và ràng buộc
- Chỉ có thể gửi tin nhắn đến nhóm mà OA đã tham gia
- Không thể gửi tin nhắn đến nhóm riêng tư mà OA chưa được mời
- Tuân thủ giới hạn tần suất gửi tin nhắn của Zalo
- Nội dung tin nhắn phải tuân thủ chính sách nội dung của Zalo

## Loại tin nhắn hỗ trợ

### 1. Text Message (Tin nhắn văn bản)
- Tin nhắn văn bản thuần túy
- Không có giới hạn độ dài cụ thể (tuân theo giới hạn chung của Zalo)

### 2. Image Message (Tin nhắn hình ảnh)
- Định dạng hỗ trợ: JPG, PNG, GIF
- Kích thước tối đa: 5MB
- Hình ảnh phải được upload trước qua Zalo Upload API

### 3. File Message (Tin nhắn file)
- Kích thước tối đa: 25MB
- Định dạng được hỗ trợ: PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, TXT, ZIP, RAR
- File phải được upload trước qua Zalo Upload API

### 4. Sticker Message (Tin nhắn sticker)
- Sử dụng sticker từ bộ sưu tập Zalo
- Sticker ID phải hợp lệ và có trong hệ thống
- Sticker không vi phạm chính sách nội dung

### 5. Mention Message (Tin nhắn mention)
- Tag/mention thành viên cụ thể trong nhóm
- Các user ID được mention phải là thành viên của nhóm
- Giới hạn số người được mention trong một tin nhắn (thường là 5-10 người)

## API Methods

### Gửi tin nhắn

#### `sendTextMessage(accessToken, groupId, message)`
Gửi tin nhắn văn bản đến nhóm.

**Parameters:**
- `accessToken` (string): Access token của Official Account
- `groupId` (string): ID của nhóm chat
- `message` (ZaloGroupTextMessage): Nội dung tin nhắn văn bản

**Returns:** `Promise<ZaloGroupMessageResult>`

#### `sendImageMessage(accessToken, groupId, message)`
Gửi tin nhắn hình ảnh đến nhóm.

**Parameters:**
- `accessToken` (string): Access token của Official Account
- `groupId` (string): ID của nhóm chat
- `message` (ZaloGroupImageMessage): Tin nhắn hình ảnh

**Returns:** `Promise<ZaloGroupMessageResult>`

#### `sendFileMessage(accessToken, groupId, message)`
Gửi tin nhắn file đến nhóm.

**Parameters:**
- `accessToken` (string): Access token của Official Account
- `groupId` (string): ID của nhóm chat
- `message` (ZaloGroupFileMessage): Tin nhắn file

**Returns:** `Promise<ZaloGroupMessageResult>`

#### `sendStickerMessage(accessToken, groupId, message)`
Gửi tin nhắn sticker đến nhóm.

**Parameters:**
- `accessToken` (string): Access token của Official Account
- `groupId` (string): ID của nhóm chat
- `message` (ZaloGroupStickerMessage): Tin nhắn sticker

**Returns:** `Promise<ZaloGroupMessageResult>`

#### `sendMentionMessage(accessToken, groupId, message)`
Gửi tin nhắn mention đến nhóm.

**Parameters:**
- `accessToken` (string): Access token của Official Account
- `groupId` (string): ID của nhóm chat
- `message` (ZaloGroupMentionMessage): Tin nhắn mention

**Returns:** `Promise<ZaloGroupMessageResult>`

### Quản lý nhóm

#### `getGroupInfo(accessToken, groupId)`
Lấy thông tin chi tiết của nhóm.

**Parameters:**
- `accessToken` (string): Access token của Official Account
- `groupId` (string): ID của nhóm chat

**Returns:** `Promise<ZaloGroupInfo>`

#### `getGroupMembers(accessToken, groupId, offset?, count?)`
Lấy danh sách thành viên của nhóm.

**Parameters:**
- `accessToken` (string): Access token của Official Account
- `groupId` (string): ID của nhóm chat
- `offset` (number, optional): Offset để phân trang (mặc định: 0)
- `count` (number, optional): Số lượng thành viên tối đa trả về (mặc định: 50, tối đa: 100)

**Returns:** `Promise<{ members: ZaloGroupMember[]; total: number }>`

#### `getJoinedGroups(accessToken, offset?, count?)`
Lấy danh sách nhóm mà OA đã tham gia.

**Parameters:**
- `accessToken` (string): Access token của Official Account
- `offset` (number, optional): Offset để phân trang (mặc định: 0)
- `count` (number, optional): Số lượng nhóm tối đa trả về (mặc định: 20, tối đa: 50)

**Returns:** `Promise<{ groups: ZaloGroupInfo[]; total: number }>`

## Yêu cầu kỹ thuật

- **HTTPS**: Sử dụng HTTPS cho tất cả API calls
- **Content-Type**: 
  - `application/json` cho text/mention messages
  - `multipart/form-data` cho file/image uploads
- **Authorization**: Access token phải được truyền trong header Authorization
- **Rate Limiting**: Tuân thủ giới hạn tần suất gửi tin nhắn của Zalo

## Xử lý lỗi

Service sử dụng `AppException` để xử lý lỗi với các mã lỗi chuẩn:
- `EXTERNAL_SERVICE_ERROR`: Lỗi từ Zalo API
- `VALIDATION_ERROR`: Lỗi validation dữ liệu đầu vào

## Ví dụ sử dụng

Xem file `examples/zalo-group-message.example.ts` để có các ví dụ chi tiết về cách sử dụng service.

## Lưu ý quan trọng

1. **Upload file trước**: Đối với image và file message, bạn phải upload file lên Zalo trước và sử dụng URL được trả về.

2. **Kiểm tra quyền**: Đảm bảo OA có đủ quyền trong nhóm trước khi gửi tin nhắn.

3. **Rate limiting**: Tránh gửi tin nhắn quá nhanh để không bị Zalo chặn.

4. **Validation**: Luôn validate dữ liệu đầu vào trước khi gọi API.

5. **Error handling**: Xử lý lỗi một cách graceful và có thông báo rõ ràng cho người dùng.

## Testing

Service đi kèm với test suite đầy đủ trong file `zalo-group-message.service.spec.ts`. Chạy test bằng lệnh:

```bash
npm run test -- zalo-group-message.service.spec.ts
```
