import { AppException } from '@common/exceptions/app.exception';
import { PaginatedResult } from '@common/response';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions/agent-error-codes';
import { McpSystemsRepository } from '@modules/agent/repositories';
import { Injectable, Logger } from '@nestjs/common';
import {
  CreateMcpSystemDto,
  McpSystemListItemDto,
  McpSystemQueryDto,
  McpSystemTrashQueryDto,
  McpSystemTrashItemDto,
  RestoreMcpSystemDto,
  RestoreMcpSystemResponseDto,
  UpdateMcpSystemDto
} from '../dto/mcp-system';
import { McpSystemMapper } from '../mappers/mcp-system.mapper';

/**
 * Service xử lý logic nghiệp vụ cho MCP Systems (Admin)
 */
@Injectable()
export class McpSystemAdminService {
  private readonly logger = new Logger(McpSystemAdminService.name);

  constructor(
    private readonly mcpSystemsRepository: McpSystemsRepository,
  ) { }

  /**
   * Tạo mới MCP system
   * @param createDto Thông tin MCP system cần tạo
   * @param employeeId ID của nhân viên tạo
   * @returns Thông tin MCP system đã tạo
   */
  async createMcpSystem(createDto: CreateMcpSystemDto, employeeId: number): Promise<McpSystemListItemDto> {
    try {
      // Kiểm tra tên server đã tồn tại chưa
      const existingSystem = await this.mcpSystemsRepository.findByNameServer(createDto.nameServer);
      if (existingSystem) {
        throw new AppException(AGENT_ERROR_CODES.MCP_SYSTEM_NAME_EXISTS);
      }

      // Tạo mới MCP system
      const mcpSystem = this.mcpSystemsRepository.create({
        nameServer: createDto.nameServer,
        description: createDto.description,
        config: createDto.config,
        createdBy: employeeId,
        updatedBy: employeeId,
      });

      const savedSystem = await this.mcpSystemsRepository.save(mcpSystem);

      this.logger.log(`Đã tạo MCP system mới: ${savedSystem.id}`);
      return McpSystemMapper.toListItemDto(savedSystem);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi tạo MCP system: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.MCP_SYSTEM_CREATION_FAILED);
    }
  }

  /**
   * Cập nhật MCP system
   * @param id ID của MCP system cần cập nhật
   * @param updateDto Thông tin cần cập nhật
   * @param employeeId ID của nhân viên cập nhật
   * @returns Thông tin MCP system sau khi cập nhật
   */
  async updateMcpSystem(id: string, updateDto: UpdateMcpSystemDto, employeeId: number): Promise<McpSystemListItemDto> {
    try {
      // Kiểm tra MCP system có tồn tại không
      const existingSystem = await this.mcpSystemsRepository.findById(id);
      if (!existingSystem) {
        throw new AppException(AGENT_ERROR_CODES.MCP_SYSTEM_NOT_FOUND);
      }

      // Kiểm tra tên server mới có trùng với system khác không
      if (updateDto.nameServer && updateDto.nameServer !== existingSystem.nameServer) {
        const nameExists = await this.mcpSystemsRepository.isNameServerExists(updateDto.nameServer, id);
        if (nameExists) {
          throw new AppException(AGENT_ERROR_CODES.MCP_SYSTEM_NAME_EXISTS);
        }
      }

      // Cập nhật thông tin
      Object.assign(existingSystem, updateDto);
      existingSystem.updatedBy = employeeId;
      const updatedSystem = await this.mcpSystemsRepository.save(existingSystem);

      this.logger.log(`Đã cập nhật MCP system: ${id}`);
      return McpSystemMapper.toListItemDto(updatedSystem);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi cập nhật MCP system ${id}: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.MCP_SYSTEM_UPDATE_FAILED);
    }
  }

  /**
   * Xóa MCP system
   * @param id ID của MCP system cần xóa
   */
  async deleteMcpSystem(id: string, employeeId: number): Promise<void> {
    try {
      // Kiểm tra MCP system có tồn tại không
      const existingSystem = await this.mcpSystemsRepository.findById(id);
      if (!existingSystem) {
        throw new AppException(AGENT_ERROR_CODES.MCP_SYSTEM_NOT_FOUND);
      }

      const success = await this.mcpSystemsRepository.softDeleteCustom(id, employeeId);
      if (!success) {
        throw new AppException(AGENT_ERROR_CODES.MCP_SYSTEM_DELETE_FAILED);
      }
      this.logger.log(`Đã xóa MCP system: ${id}`);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi xóa MCP system ${id}: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.MCP_SYSTEM_DELETE_FAILED);
    }
  }

  /**
   * Lấy danh sách MCP systems có phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách MCP systems có phân trang
   */
  async getMcpSystems(queryDto: McpSystemQueryDto): Promise<PaginatedResult<McpSystemListItemDto>> {
    const result = await this.mcpSystemsRepository.findPaginated(
      queryDto.page,
      queryDto.limit,
      queryDto.search,
      queryDto.sortBy,
      queryDto.sortDirection,
    );

    return {
      items: McpSystemMapper.toListItemDtos(result.items),
      meta: result.meta,
    };
  }

  /**
   * Lấy danh sách MCP systems đã xóa có phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách MCP systems đã xóa có phân trang
   */
  async getDeletedMcpSystems(queryDto: McpSystemTrashQueryDto): Promise<PaginatedResult<McpSystemTrashItemDto>> {
    const result = await this.mcpSystemsRepository.findDeletedPaginated(
      queryDto.page,
      queryDto.limit,
      queryDto.search,
      queryDto.sortBy,
      queryDto.sortDirection,
    );

    return {
      items: McpSystemMapper.toTrashItemDtos(result.items),
      meta: result.meta,
    };
  }

  /**
   * Khôi phục MCP systems từ trash
   * @param restoreDto Danh sách ID cần khôi phục
   * @param employeeId ID của nhân viên thực hiện khôi phục
   * @returns Kết quả khôi phục
   */
  async restoreMcpSystems(restoreDto: RestoreMcpSystemDto, employeeId: number): Promise<RestoreMcpSystemResponseDto> {
    try {
      // Kiểm tra các MCP systems có tồn tại trong trash không
      const deletedSystems = await this.mcpSystemsRepository.findDeletedPaginated(
        1,
        restoreDto.ids.length,
        undefined,
        'id',
        'ASC'
      );

      const existingIds = deletedSystems.items
        .filter(system => restoreDto.ids.includes(system.id))
        .map(system => system.id);

      if (existingIds.length === 0) {
        throw new AppException(AGENT_ERROR_CODES.MCP_SYSTEM_NOT_FOUND);
      }

      // Thực hiện khôi phục
      const restoredCount = await this.mcpSystemsRepository.bulkRestore(existingIds, employeeId);

      this.logger.log(`Đã khôi phục ${restoredCount} MCP systems`);

      return {
        restoredCount,
        restoredIds: existingIds,
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi khôi phục MCP systems: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.MCP_SYSTEM_UPDATE_FAILED);
    }
  }
}
