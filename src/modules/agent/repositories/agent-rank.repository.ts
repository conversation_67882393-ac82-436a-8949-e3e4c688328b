import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { AgentRank } from '@modules/agent/entities';
import { PaginatedResult } from '@common/response';

/**
 * Repository cho AgentRank
 * Xử lý các thao tác CRUD và truy vấn nâng cao liên quan đến cấp bậc của agent
 */
@Injectable()
export class AgentRankRepository extends Repository<AgentRank> {
  private readonly logger = new Logger(AgentRankRepository.name);

  constructor(private dataSource: DataSource) {
    super(AgentRank, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho AgentRank
   * @returns SelectQueryBuilder cho AgentRank
   */
  private createBaseQuery(): SelectQueryBuilder<AgentRank> {
    return this.createQueryBuilder('agentRank');
  }

  /**
   * Tì<PERSON> cấp bậc theo ID
   * @param id ID của cấp bậc
   * @returns AgentRank nếu tìm thấy, null nếu không tìm thấy
   */
  async findById(id: number): Promise<AgentRank | null> {
    return this.createBaseQuery()
      .where('agentRank.id = :id', { id })
      .getOne();
  }

  /**
   * Tìm cấp bậc theo tên
   * @param name Tên của cấp bậc
   * @returns AgentRank nếu tìm thấy, null nếu không tìm thấy
   */
  async findByName(name: string): Promise<AgentRank | null> {
    return this.createBaseQuery()
      .where('agentRank.name = :name', { name })
      .getOne();
  }



  /**
   * Kiểm tra xem khoảng exp có chồng chéo với bất kỳ khoảng exp nào khác không
   * @param minExp Điểm kinh nghiệm tối thiểu
   * @param maxExp Điểm kinh nghiệm tối đa
   * @param excludeId ID của cấp bậc cần loại trừ (dùng khi cập nhật)
   * @returns true nếu có chồng chéo, false nếu không
   */
  async checkOverlap(minExp: number, maxExp: number, excludeId?: number): Promise<boolean> {
    const qb = this.createBaseQuery();

    // Điều kiện kiểm tra chồng chéo:
    // 1. minExp nằm trong khoảng [agentRank.minExp, agentRank.maxExp)
    // 2. maxExp nằm trong khoảng (agentRank.minExp, agentRank.maxExp]
    // 3. minExp <= agentRank.minExp và maxExp >= agentRank.maxExp
    qb.where(
      '(:minExp >= agentRank.minExp AND :minExp < agentRank.maxExp) OR ' +
      '(:maxExp > agentRank.minExp AND :maxExp <= agentRank.maxExp) OR ' +
      '(:minExp <= agentRank.minExp AND :maxExp >= agentRank.maxExp)',
      { minExp, maxExp }
    );

    // Loại trừ cấp bậc đang cập nhật (nếu có)
    if (excludeId) {
      qb.andWhere('agentRank.id != :excludeId', { excludeId });
    }

    const count = await qb.getCount();
    return count > 0;
  }

  /**
   * Lấy danh sách cấp bậc có phân trang
   * @param page Số trang
   * @param limit Số lượng item trên một trang
   * @param search Từ khóa tìm kiếm (tùy chọn)
   * @param sortBy Trường sắp xếp (tùy chọn)
   * @param sortDirection Hướng sắp xếp (tùy chọn)
   * @param active Lọc theo trạng thái kích hoạt (tùy chọn)
   * @returns Danh sách cấp bậc có phân trang
   */
  async findPaginated(
    page: number,
    limit: number,
    search?: string,
    sortBy: string = 'minExp',
    sortDirection: 'ASC' | 'DESC' = 'ASC',
    active?: boolean,
  ): Promise<PaginatedResult<AgentRank>> {
    try {
      const qb = this.createBaseQuery();

      // Thêm điều kiện tìm kiếm nếu có
      if (search) {
        qb.andWhere('(agentRank.name ILIKE :search OR agentRank.description ILIKE :search)',
          { search: `%${search}%` });
      }

      // Lọc theo trạng thái kích hoạt nếu có
      if (active !== undefined) {
        qb.andWhere('agentRank.active = :active', { active });
      }

      // Xác định trường sắp xếp hợp lệ
      const validSortColumns = ['name', 'minExp', 'maxExp', 'id'];

      // Nếu sortBy là createdAt, sử dụng id thay thế (giả định id tăng dần theo thời gian tạo)
      let effectiveSortColumn = sortBy === 'createdAt' ? 'id' : sortBy;

      // Đảm bảo sử dụng trường hợp lệ
      effectiveSortColumn = validSortColumns.includes(effectiveSortColumn) ? effectiveSortColumn : 'id';

      // Thêm phân trang và sắp xếp
      qb.skip((page - 1) * limit)
        .take(limit)
        .orderBy(effectiveSortColumn, sortDirection);

      // Lấy kết quả
      const [items, total] = await qb.getManyAndCount();

      return {
        items,
        meta: {
          totalItems: total,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(total / limit),
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi khi truy vấn danh sách cấp bậc: ${error.message}`);
      throw error;
    }
  }

  /**
   * Lấy tất cả cấp bậc, sắp xếp theo điểm kinh nghiệm tối thiểu
   * @returns Danh sách tất cả cấp bậc
   */
  async findAll(): Promise<AgentRank[]> {
    return this.createBaseQuery()
      .orderBy('agentRank.minExp', 'ASC')
      .getMany();
  }
}
