import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsString, ArrayMinSize, ArrayMaxSize, IsNotEmpty } from 'class-validator';

/**
 * DTO cho việc xóa nhiều Official Accounts
 */
export class BulkDeleteOfficialAccountsDto {
  @ApiProperty({
    description: 'Danh sách ID của các Official Account cần xóa',
    example: ['oa_123456789', 'oa_987654321'],
    type: [String],
    minItems: 1,
    maxItems: 50,
  })
  @IsArray({ message: 'oaIds phải là một mảng' })
  @ArrayMinSize(1, { message: 'Phải có ít nhất 1 Official Account để xóa' })
  @ArrayMaxSize(50, { message: 'Không thể xóa quá 50 Official Account cùng lúc' })
  @IsString({ each: true, message: 'Mỗi oaId phải là một chuỗi' })
  @IsNotEmpty({ each: true, message: 'oaId không được để trống' })
  oaIds: string[];
}

/**
 * DTO cho kết quả xóa nhiều Official Accounts
 */
export class BulkDeleteOfficialAccountsResponseDto {
  @ApiProperty({
    description: 'Tổng số Official Account được yêu cầu xóa',
    example: 5,
  })
  totalRequested: number;

  @ApiProperty({
    description: 'Số Official Account xóa thành công',
    example: 4,
  })
  successCount: number;

  @ApiProperty({
    description: 'Số Official Account xóa thất bại',
    example: 1,
  })
  failureCount: number;

  @ApiProperty({
    description: 'Danh sách Official Account xóa thành công',
    example: ['oa_123456789', 'oa_987654321', 'oa_111222333'],
    type: [String],
  })
  successfulDeletes: string[];

  @ApiProperty({
    description: 'Danh sách Official Account xóa thất bại với lý do',
    example: [
      {
        oaId: 'oa_444555666',
        reason: 'Không tìm thấy Official Account'
      }
    ],
    type: 'array',
    items: {
      type: 'object',
      properties: {
        oaId: { type: 'string' },
        reason: { type: 'string' }
      }
    }
  })
  failedDeletes: Array<{
    oaId: string;
    reason: string;
  }>;

  @ApiProperty({
    description: 'Thông báo tổng kết',
    example: 'Đã xóa thành công 4/5 Official Account',
  })
  message: string;
}
