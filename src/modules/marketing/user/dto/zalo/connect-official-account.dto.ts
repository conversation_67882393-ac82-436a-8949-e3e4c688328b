import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsString, IsOptional, IsUrl } from 'class-validator';

/**
 * DTO cho việc kết n<PERSON>i Official Account
 */
export class ConnectOfficialAccountDto {
  @ApiProperty({
    description: 'Access token của Official Account',
    example: 'abcdef123456789',
  })
  @IsString()
  @IsNotEmpty()
  accessToken: string;

  @ApiProperty({
    description: 'Refresh token của Official Account',
    example: 'refresh123456789',
  })
  @IsString()
  @IsNotEmpty()
  refreshToken: string;

  @ApiProperty({
    description: 'Thời gian hết hạn của access token (Unix timestamp)',
    example: *************,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  expiresAt?: number;
}

/**
 * DTO cho việc lấy URL OAuth Zalo
 */
export class GetZaloOAuthUrlDto {
  @ApiProperty({
    description: 'URL redirect sau khi OAuth thành công',
    example: 'https://v2.redai.vn/integration/zalo/oa',
    required: false,
  })
  @IsUrl()
  @IsOptional()
  redirectUri?: string;
}

/**
 * DTO cho callback OAuth Zalo
 */
export class ZaloOAuthCallbackDto {
  @ApiProperty({
    description: 'Authorization code từ Zalo',
    example: 'auth_code_123456',
  })
  @IsString()
  @IsNotEmpty()
  code: string;

  @ApiProperty({
    description: 'State parameter để xác thực (được tự tạo với tiền tố zalo_integration_oa)',
    example: 'zalo_integration_oa_1640995200000_abc123xyz',
    required: false,
  })
  @IsString()
  @IsOptional()
  state?: string;
}

/**
 * Response DTO cho URL OAuth
 */
export class ZaloOAuthUrlResponseDto {
  @ApiProperty({
    description: 'URL OAuth để redirect người dùng',
    example: 'https://oauth.zaloapp.com/v4/oa/permission?app_id=123&redirect_uri=...',
  })
  oauthUrl: string;

  @ApiProperty({
    description: 'State parameter được tự tạo với tiền tố zalo_integration_oa',
    example: 'zalo_integration_oa_1640995200000_abc123xyz',
  })
  state: string;
}
