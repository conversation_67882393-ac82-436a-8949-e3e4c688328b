import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional, IsString, IsObject, ValidateIf } from 'class-validator';

/**
 * Enum cho loại tin nhắn giao dịch
 */
export enum TransactionMessageType {
  ORDER_CONFIRMATION = 'order_confirmation',
  DELIVERY_UPDATE = 'delivery_update',
  PAYMENT_NOTIFICATION = 'payment_notification',
  CUSTOM_TEMPLATE = 'custom_template',
}

/**
 * Enum cho trạng thái thanh toán
 */
export enum PaymentStatus {
  SUCCESS = 'success',
  FAILED = 'failed',
  PENDING = 'pending',
}

/**
 * DTO cho việc gửi tin nhắn giao dịch tùy chỉnh
 */
export class SendTransactionMessageDto {
  @ApiProperty({
    description: 'ID của người dùng Zalo',
    example: '123456789',
  })
  @IsString()
  @IsNotEmpty()
  userId: string;

  @ApiProperty({
    description: 'ID của template giao dịch đã được phê duyệt',
    example: 'template_order_confirmation_001',
  })
  @IsString()
  @IsNotEmpty()
  templateId: string;

  @ApiProperty({
    description: 'Dữ liệu để điền vào template',
    example: {
      order_id: 'ORD001',
      customer_name: 'Nguyễn Văn A',
      total_amount: '500,000 VND'
    },
  })
  @IsObject()
  @IsNotEmpty()
  templateData: Record<string, string>;

  @ApiProperty({
    description: 'Chế độ gửi tin nhắn',
    enum: ['development', 'production'],
    example: 'production',
    required: false,
  })
  @IsOptional()
  @IsEnum(['development', 'production'])
  mode?: 'development' | 'production';
}

/**
 * DTO cho việc gửi tin nhắn xác nhận đơn hàng
 */
export class SendOrderConfirmationDto {
  @ApiProperty({
    description: 'ID của người dùng Zalo',
    example: '123456789',
  })
  @IsString()
  @IsNotEmpty()
  userId: string;

  @ApiProperty({
    description: 'ID đơn hàng',
    example: 'ORD001',
  })
  @IsString()
  @IsNotEmpty()
  orderId: string;

  @ApiProperty({
    description: 'Ngày đặt hàng (định dạng: DD/MM/YYYY)',
    example: '15/12/2024',
  })
  @IsString()
  @IsNotEmpty()
  orderDate: string;

  @ApiProperty({
    description: 'Tên khách hàng',
    example: 'Nguyễn Văn A',
  })
  @IsString()
  @IsNotEmpty()
  customerName: string;

  @ApiProperty({
    description: 'Tổng tiền đơn hàng',
    example: '500,000 VND',
  })
  @IsString()
  @IsNotEmpty()
  totalAmount: string;

  @ApiProperty({
    description: 'Phương thức thanh toán',
    example: 'Chuyển khoản ngân hàng',
  })
  @IsString()
  @IsNotEmpty()
  paymentMethod: string;

  @ApiProperty({
    description: 'Địa chỉ giao hàng',
    example: '123 Đường ABC, Quận 1, TP.HCM',
    required: false,
  })
  @IsOptional()
  @IsString()
  deliveryAddress?: string;

  @ApiProperty({
    description: 'Thời gian giao hàng dự kiến',
    example: '18/12/2024',
    required: false,
  })
  @IsOptional()
  @IsString()
  estimatedDelivery?: string;
}

/**
 * DTO cho việc gửi tin nhắn cập nhật giao hàng
 */
export class SendDeliveryUpdateDto {
  @ApiProperty({
    description: 'ID của người dùng Zalo',
    example: '123456789',
  })
  @IsString()
  @IsNotEmpty()
  userId: string;

  @ApiProperty({
    description: 'ID đơn hàng',
    example: 'ORD001',
  })
  @IsString()
  @IsNotEmpty()
  orderId: string;

  @ApiProperty({
    description: 'Trạng thái giao hàng',
    example: 'Đang giao hàng',
  })
  @IsString()
  @IsNotEmpty()
  status: string;

  @ApiProperty({
    description: 'Mã vận đơn',
    example: 'VD123456789',
    required: false,
  })
  @IsOptional()
  @IsString()
  trackingNumber?: string;

  @ApiProperty({
    description: 'Thời gian giao hàng dự kiến',
    example: '18/12/2024 14:00',
    required: false,
  })
  @IsOptional()
  @IsString()
  estimatedDelivery?: string;

  @ApiProperty({
    description: 'Ghi chú giao hàng',
    example: 'Shipper sẽ gọi điện trước khi giao',
    required: false,
  })
  @IsOptional()
  @IsString()
  deliveryNote?: string;
}

/**
 * DTO cho việc gửi tin nhắn thông báo thanh toán
 */
export class SendPaymentNotificationDto {
  @ApiProperty({
    description: 'ID của người dùng Zalo',
    example: '123456789',
  })
  @IsString()
  @IsNotEmpty()
  userId: string;

  @ApiProperty({
    description: 'ID đơn hàng',
    example: 'ORD001',
  })
  @IsString()
  @IsNotEmpty()
  orderId: string;

  @ApiProperty({
    description: 'Số tiền thanh toán',
    example: '500,000 VND',
  })
  @IsString()
  @IsNotEmpty()
  amount: string;

  @ApiProperty({
    description: 'Phương thức thanh toán',
    example: 'Chuyển khoản ngân hàng',
  })
  @IsString()
  @IsNotEmpty()
  paymentMethod: string;

  @ApiProperty({
    description: 'Ngày thanh toán',
    example: '15/12/2024 10:30',
  })
  @IsString()
  @IsNotEmpty()
  paymentDate: string;

  @ApiProperty({
    description: 'Trạng thái thanh toán',
    enum: PaymentStatus,
    example: PaymentStatus.SUCCESS,
  })
  @IsEnum(PaymentStatus)
  @IsNotEmpty()
  status: PaymentStatus;

  @ApiProperty({
    description: 'ID giao dịch',
    example: 'TXN123456789',
    required: false,
  })
  @IsOptional()
  @IsString()
  transactionId?: string;
}

/**
 * DTO cho phản hồi gửi tin nhắn giao dịch
 */
export class SendTransactionMessageResponseDto {
  @ApiProperty({
    description: 'ID của tin nhắn đã gửi',
    example: 'msg_987654321',
  })
  messageId: string;

  @ApiProperty({
    description: 'Trạng thái gửi tin nhắn',
    example: 'sent',
  })
  status: string;

  @ApiProperty({
    description: 'Thời gian gửi tin nhắn (Unix timestamp)',
    example: 1625097600000,
  })
  sentAt: number;

  @ApiProperty({
    description: 'Loại tin nhắn giao dịch',
    example: 'order_confirmation',
  })
  messageType: string;
}

/**
 * DTO cho việc gửi tin nhắn giao dịch linh hoạt
 */
export class SendFlexibleTransactionMessageDto {
  @ApiProperty({
    description: 'ID của người dùng Zalo',
    example: '123456789',
  })
  @IsString()
  @IsNotEmpty()
  userId: string;

  @ApiProperty({
    description: 'Loại tin nhắn giao dịch',
    enum: TransactionMessageType,
    example: TransactionMessageType.ORDER_CONFIRMATION,
  })
  @IsEnum(TransactionMessageType)
  @IsNotEmpty()
  messageType: TransactionMessageType;

  // Các trường cho ORDER_CONFIRMATION
  @ValidateIf(o => o.messageType === TransactionMessageType.ORDER_CONFIRMATION)
  @IsString()
  @IsNotEmpty()
  orderId?: string;

  @ValidateIf(o => o.messageType === TransactionMessageType.ORDER_CONFIRMATION)
  @IsString()
  @IsNotEmpty()
  orderDate?: string;

  @ValidateIf(o => o.messageType === TransactionMessageType.ORDER_CONFIRMATION)
  @IsString()
  @IsNotEmpty()
  customerName?: string;

  @ValidateIf(o => o.messageType === TransactionMessageType.ORDER_CONFIRMATION)
  @IsString()
  @IsNotEmpty()
  totalAmount?: string;

  @ValidateIf(o => o.messageType === TransactionMessageType.ORDER_CONFIRMATION)
  @IsString()
  @IsNotEmpty()
  paymentMethod?: string;

  @ValidateIf(o => o.messageType === TransactionMessageType.ORDER_CONFIRMATION)
  @IsString()
  @IsOptional()
  deliveryAddress?: string;

  @ValidateIf(o => o.messageType === TransactionMessageType.ORDER_CONFIRMATION)
  @IsString()
  @IsOptional()
  estimatedDelivery?: string;

  // Các trường cho CUSTOM_TEMPLATE
  @ValidateIf(o => o.messageType === TransactionMessageType.CUSTOM_TEMPLATE)
  @IsString()
  @IsNotEmpty()
  templateId?: string;

  @ValidateIf(o => o.messageType === TransactionMessageType.CUSTOM_TEMPLATE)
  @IsObject()
  @IsNotEmpty()
  templateData?: Record<string, string>;
}
