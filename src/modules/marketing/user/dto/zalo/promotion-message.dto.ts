import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional, IsString, IsObject, ValidateIf, IsUrl } from 'class-validator';

/**
 * Enum cho loại tin nhắn truyền thông
 */
export enum PromotionMessageType {
  PROMOTION_CAMPAIGN = 'promotion_campaign',
  EVENT_NOTIFICATION = 'event_notification',
  CUSTOM_TEMPLATE = 'custom_template',
}

/**
 * DTO cho việc gửi tin nhắn truyền thông tùy chỉnh
 */
export class SendPromotionMessageDto {
  @ApiProperty({
    description: 'ID của người dùng Zalo',
    example: '123456789',
  })
  @IsString()
  @IsNotEmpty()
  userId: string;

  @ApiProperty({
    description: 'ID của template truyền thông đã được phê duyệt',
    example: 'template_promotion_001',
  })
  @IsString()
  @IsNotEmpty()
  templateId: string;

  @ApiProperty({
    description: 'Dữ liệu để điền vào template',
    example: {
      title: 'Khuyến mãi đặc biệt',
      description: 'Giảm giá 50% tất cả sản phẩm',
      valid_until: '31/12/2024'
    },
  })
  @IsObject()
  @IsNotEmpty()
  templateData: Record<string, string>;

  @ApiProperty({
    description: 'Chế độ gửi tin nhắn',
    enum: ['development', 'production'],
    example: 'production',
    required: false,
  })
  @IsOptional()
  @IsEnum(['development', 'production'])
  mode?: 'development' | 'production';
}

/**
 * DTO cho việc gửi tin nhắn khuyến mãi
 */
export class SendPromotionCampaignDto {
  @ApiProperty({
    description: 'ID của người dùng Zalo',
    example: '123456789',
  })
  @IsString()
  @IsNotEmpty()
  userId: string;

  @ApiProperty({
    description: 'Tiêu đề khuyến mãi',
    example: 'Flash Sale - Giảm giá 50%',
  })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({
    description: 'Mô tả khuyến mãi',
    example: 'Khuyến mãi đặc biệt chỉ trong hôm nay! Giảm giá 50% tất cả sản phẩm thời trang.',
  })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({
    description: 'Phần trăm giảm giá',
    example: '50%',
    required: false,
  })
  @IsOptional()
  @IsString()
  discountPercent?: string;

  @ApiProperty({
    description: 'Giá gốc',
    example: '1,000,000 VND',
    required: false,
  })
  @IsOptional()
  @IsString()
  originalPrice?: string;

  @ApiProperty({
    description: 'Giá khuyến mãi',
    example: '500,000 VND',
    required: false,
  })
  @IsOptional()
  @IsString()
  salePrice?: string;

  @ApiProperty({
    description: 'Thời hạn khuyến mãi',
    example: '31/12/2024 23:59',
    required: false,
  })
  @IsOptional()
  @IsString()
  validUntil?: string;

  @ApiProperty({
    description: 'Mã khuyến mãi',
    example: 'SALE50',
    required: false,
  })
  @IsOptional()
  @IsString()
  promoCode?: string;

  @ApiProperty({
    description: 'URL hình ảnh khuyến mãi',
    example: 'https://example.com/promotion-banner.jpg',
    required: false,
  })
  @IsOptional()
  @IsUrl()
  imageUrl?: string;

  @ApiProperty({
    description: 'URL hành động (link mua hàng)',
    example: 'https://example.com/shop',
    required: false,
  })
  @IsOptional()
  @IsUrl()
  actionUrl?: string;
}

/**
 * DTO cho việc gửi thông báo sự kiện
 */
export class SendEventNotificationDto {
  @ApiProperty({
    description: 'ID của người dùng Zalo',
    example: '123456789',
  })
  @IsString()
  @IsNotEmpty()
  userId: string;

  @ApiProperty({
    description: 'Tên sự kiện',
    example: 'Hội thảo Digital Marketing 2024',
  })
  @IsString()
  @IsNotEmpty()
  eventName: string;

  @ApiProperty({
    description: 'Ngày sự kiện',
    example: '25/12/2024',
  })
  @IsString()
  @IsNotEmpty()
  eventDate: string;

  @ApiProperty({
    description: 'Thời gian sự kiện',
    example: '14:00 - 17:00',
    required: false,
  })
  @IsOptional()
  @IsString()
  eventTime?: string;

  @ApiProperty({
    description: 'Địa điểm tổ chức',
    example: 'Trung tâm Hội nghị Quốc gia, Hà Nội',
    required: false,
  })
  @IsOptional()
  @IsString()
  location?: string;

  @ApiProperty({
    description: 'Mô tả sự kiện',
    example: 'Hội thảo chia sẻ xu hướng Digital Marketing mới nhất và các chiến lược hiệu quả.',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'URL đăng ký tham gia',
    example: 'https://example.com/event-registration',
    required: false,
  })
  @IsOptional()
  @IsUrl()
  registrationUrl?: string;

  @ApiProperty({
    description: 'URL hình ảnh sự kiện',
    example: 'https://example.com/event-banner.jpg',
    required: false,
  })
  @IsOptional()
  @IsUrl()
  imageUrl?: string;
}

/**
 * DTO cho phản hồi gửi tin nhắn truyền thông
 */
export class SendPromotionMessageResponseDto {
  @ApiProperty({
    description: 'ID của tin nhắn đã gửi',
    example: 'msg_987654321',
  })
  messageId: string;

  @ApiProperty({
    description: 'Trạng thái gửi tin nhắn',
    example: 'sent',
  })
  status: string;

  @ApiProperty({
    description: 'Thời gian gửi tin nhắn (Unix timestamp)',
    example: 1625097600000,
  })
  sentAt: number;

  @ApiProperty({
    description: 'Loại tin nhắn truyền thông',
    example: 'promotion_campaign',
  })
  messageType: string;

  @ApiProperty({
    description: 'Cảnh báo về thời gian gửi (nếu có)',
    example: 'Tin nhắn được gửi ngoài khung giờ khuyến nghị (8:00-22:00)',
    required: false,
  })
  warning?: string;
}

/**
 * DTO cho việc gửi tin nhắn truyền thông linh hoạt
 */
export class SendFlexiblePromotionMessageDto {
  @ApiProperty({
    description: 'ID của người dùng Zalo',
    example: '123456789',
  })
  @IsString()
  @IsNotEmpty()
  userId: string;

  @ApiProperty({
    description: 'Loại tin nhắn truyền thông',
    enum: PromotionMessageType,
    example: PromotionMessageType.PROMOTION_CAMPAIGN,
  })
  @IsEnum(PromotionMessageType)
  @IsNotEmpty()
  messageType: PromotionMessageType;

  // Các trường cho PROMOTION_CAMPAIGN
  @ValidateIf(o => o.messageType === PromotionMessageType.PROMOTION_CAMPAIGN)
  @IsString()
  @IsNotEmpty()
  title?: string;

  @ValidateIf(o => o.messageType === PromotionMessageType.PROMOTION_CAMPAIGN)
  @IsString()
  @IsNotEmpty()
  description?: string;

  @ValidateIf(o => o.messageType === PromotionMessageType.PROMOTION_CAMPAIGN)
  @IsString()
  @IsOptional()
  discountPercent?: string;

  @ValidateIf(o => o.messageType === PromotionMessageType.PROMOTION_CAMPAIGN)
  @IsString()
  @IsOptional()
  originalPrice?: string;

  @ValidateIf(o => o.messageType === PromotionMessageType.PROMOTION_CAMPAIGN)
  @IsString()
  @IsOptional()
  salePrice?: string;

  @ValidateIf(o => o.messageType === PromotionMessageType.PROMOTION_CAMPAIGN)
  @IsString()
  @IsOptional()
  validUntil?: string;

  @ValidateIf(o => o.messageType === PromotionMessageType.PROMOTION_CAMPAIGN)
  @IsString()
  @IsOptional()
  promoCode?: string;

  @ValidateIf(o => o.messageType === PromotionMessageType.PROMOTION_CAMPAIGN)
  @IsUrl()
  @IsOptional()
  imageUrl?: string;

  @ValidateIf(o => o.messageType === PromotionMessageType.PROMOTION_CAMPAIGN)
  @IsUrl()
  @IsOptional()
  actionUrl?: string;

  // Các trường cho EVENT_NOTIFICATION
  @ValidateIf(o => o.messageType === PromotionMessageType.EVENT_NOTIFICATION)
  @IsString()
  @IsNotEmpty()
  eventName?: string;

  @ValidateIf(o => o.messageType === PromotionMessageType.EVENT_NOTIFICATION)
  @IsString()
  @IsNotEmpty()
  eventDate?: string;

  @ValidateIf(o => o.messageType === PromotionMessageType.EVENT_NOTIFICATION)
  @IsString()
  @IsOptional()
  eventTime?: string;

  @ValidateIf(o => o.messageType === PromotionMessageType.EVENT_NOTIFICATION)
  @IsString()
  @IsOptional()
  location?: string;

  @ValidateIf(o => o.messageType === PromotionMessageType.EVENT_NOTIFICATION)
  @IsUrl()
  @IsOptional()
  registrationUrl?: string;

  // Các trường cho CUSTOM_TEMPLATE
  @ValidateIf(o => o.messageType === PromotionMessageType.CUSTOM_TEMPLATE)
  @IsString()
  @IsNotEmpty()
  templateId?: string;

  @ValidateIf(o => o.messageType === PromotionMessageType.CUSTOM_TEMPLATE)
  @IsObject()
  @IsNotEmpty()
  templateData?: Record<string, string>;
}
