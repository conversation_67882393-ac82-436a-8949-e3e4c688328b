import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional, IsString, ValidateIf, IsObject } from 'class-validator';

/**
 * Enum cho loại tin nhắn tư vấn
 */
export enum ConsultationMessageType {
  TEXT = 'text',
  IMAGE = 'image',
  QUOTE = 'quote',
  STICKER = 'sticker',
  FILE = 'file',
  REQUEST_INFO = 'request_info',
}

/**
 * DTO cho thông tin tin nhắn được trích dẫn
 */
export class QuoteMessageDto {
  @ApiProperty({
    description: 'ID của tin nhắn được trích dẫn',
    example: 'msg_123456789',
  })
  @IsString()
  @IsNotEmpty()
  messageId: string;

  @ApiProperty({
    description: 'Nội dung tin nhắn được trích dẫn',
    example: 'Tôi muốn hỏi về sản phẩm này',
  })
  @IsString()
  @IsNotEmpty()
  content: string;
}

/**
 * DTO cho thông tin trường yêu cầu thông tin người dùng
 */
export class RequestInfoElementDto {
  @ApiProperty({
    description: 'Tiêu đề của trường',
    example: 'Họ và tên',
  })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({
    description: 'Loại input',
    enum: ['text', 'phone', 'email', 'date'],
    example: 'text',
  })
  @IsString()
  @IsNotEmpty()
  type: 'text' | 'phone' | 'email' | 'date';

  @ApiProperty({
    description: 'Có bắt buộc nhập không',
    example: true,
    required: false,
  })
  @IsOptional()
  required?: boolean;

  @ApiProperty({
    description: 'Placeholder text',
    example: 'Nhập họ và tên của bạn',
    required: false,
  })
  @IsOptional()
  @IsString()
  placeholder?: string;
}

/**
 * DTO cho việc gửi tin nhắn tư vấn
 */
export class SendConsultationMessageDto {
  @ApiProperty({
    description: 'ID của người dùng Zalo',
    example: '123456789',
  })
  @IsString()
  @IsNotEmpty()
  userId: string;

  @ApiProperty({
    description: 'Loại tin nhắn tư vấn',
    enum: ConsultationMessageType,
    example: ConsultationMessageType.TEXT,
  })
  @IsEnum(ConsultationMessageType)
  @IsNotEmpty()
  messageType: ConsultationMessageType;

  @ApiProperty({
    description: 'Nội dung tin nhắn văn bản (bắt buộc cho TEXT và QUOTE)',
    example: 'Xin chào! Cảm ơn bạn đã liên hệ với chúng tôi.',
    required: false,
  })
  @ValidateIf(o => o.messageType === ConsultationMessageType.TEXT || o.messageType === ConsultationMessageType.QUOTE)
  @IsString()
  @IsNotEmpty()
  text?: string;

  @ApiProperty({
    description: 'URL của hình ảnh (bắt buộc cho IMAGE)',
    example: 'https://example.com/image.jpg',
    required: false,
  })
  @ValidateIf(o => o.messageType === ConsultationMessageType.IMAGE)
  @IsString()
  @IsNotEmpty()
  imageUrl?: string;

  @ApiProperty({
    description: 'Nội dung tin nhắn kèm theo hình ảnh (tùy chọn cho IMAGE)',
    example: 'Đây là hình ảnh sản phẩm mới nhất của chúng tôi',
    required: false,
  })
  @ValidateIf(o => o.messageType === ConsultationMessageType.IMAGE)
  @IsString()
  @IsOptional()
  imageMessage?: string;

  @ApiProperty({
    description: 'ID của sticker (bắt buộc cho STICKER)',
    example: 'sticker_123',
    required: false,
  })
  @ValidateIf(o => o.messageType === ConsultationMessageType.STICKER)
  @IsString()
  @IsNotEmpty()
  stickerId?: string;

  @ApiProperty({
    description: 'Nội dung tin nhắn kèm theo sticker (tùy chọn cho STICKER)',
    example: 'Cảm ơn bạn!',
    required: false,
  })
  @ValidateIf(o => o.messageType === ConsultationMessageType.STICKER)
  @IsString()
  @IsOptional()
  stickerMessage?: string;

  @ApiProperty({
    description: 'Thông tin tin nhắn được trích dẫn (bắt buộc cho QUOTE)',
    type: QuoteMessageDto,
    required: false,
  })
  @ValidateIf(o => o.messageType === ConsultationMessageType.QUOTE)
  @IsObject()
  @IsNotEmpty()
  quote?: QuoteMessageDto;

  @ApiProperty({
    description: 'URL của file đính kèm (bắt buộc cho FILE)',
    example: 'https://example.com/document.pdf',
    required: false,
  })
  @ValidateIf(o => o.messageType === ConsultationMessageType.FILE)
  @IsString()
  @IsNotEmpty()
  fileUrl?: string;

  @ApiProperty({
    description: 'Tên file đính kèm (bắt buộc cho FILE)',
    example: 'document.pdf',
    required: false,
  })
  @ValidateIf(o => o.messageType === ConsultationMessageType.FILE)
  @IsString()
  @IsNotEmpty()
  filename?: string;

  @ApiProperty({
    description: 'Nội dung tin nhắn kèm theo file (tùy chọn cho FILE)',
    example: 'Đây là tài liệu hướng dẫn sử dụng sản phẩm',
    required: false,
  })
  @ValidateIf(o => o.messageType === ConsultationMessageType.FILE)
  @IsString()
  @IsOptional()
  fileMessage?: string;

  @ApiProperty({
    description: 'Tiêu đề form yêu cầu thông tin (bắt buộc cho REQUEST_INFO)',
    example: 'Thông tin liên hệ',
    required: false,
  })
  @ValidateIf(o => o.messageType === ConsultationMessageType.REQUEST_INFO)
  @IsString()
  @IsNotEmpty()
  requestTitle?: string;

  @ApiProperty({
    description: 'Mô tả ngắn gọn về form (tùy chọn cho REQUEST_INFO)',
    example: 'Vui lòng cung cấp thông tin để chúng tôi hỗ trợ bạn tốt hơn',
    required: false,
  })
  @ValidateIf(o => o.messageType === ConsultationMessageType.REQUEST_INFO)
  @IsString()
  @IsOptional()
  requestSubtitle?: string;

  @ApiProperty({
    description: 'URL hình ảnh đại diện cho form (tùy chọn cho REQUEST_INFO)',
    example: 'https://example.com/form-image.jpg',
    required: false,
  })
  @ValidateIf(o => o.messageType === ConsultationMessageType.REQUEST_INFO)
  @IsString()
  @IsOptional()
  requestImageUrl?: string;

  @ApiProperty({
    description: 'Danh sách các trường thông tin cần thu thập (bắt buộc cho REQUEST_INFO)',
    type: [RequestInfoElementDto],
    required: false,
  })
  @ValidateIf(o => o.messageType === ConsultationMessageType.REQUEST_INFO)
  @IsObject({ each: true })
  @IsNotEmpty()
  requestElements?: RequestInfoElementDto[];
}

/**
 * DTO cho phản hồi gửi tin nhắn tư vấn
 */
export class SendConsultationMessageResponseDto {
  @ApiProperty({
    description: 'ID của tin nhắn đã gửi',
    example: 'msg_987654321',
  })
  messageId: string;

  @ApiProperty({
    description: 'Trạng thái gửi tin nhắn',
    example: 'sent',
  })
  status: string;

  @ApiProperty({
    description: 'Thời gian gửi tin nhắn (Unix timestamp)',
    example: 1625097600000,
  })
  sentAt: number;
}

/**
 * DTO cho việc gửi tin nhắn tư vấn hàng loạt
 */
export class SendBulkConsultationMessageDto {
  @ApiProperty({
    description: 'Danh sách ID người dùng Zalo',
    example: ['123456789', '987654321', '456789123'],
    type: [String],
  })
  @IsString({ each: true })
  @IsNotEmpty()
  userIds: string[];

  @ApiProperty({
    description: 'Loại tin nhắn tư vấn',
    enum: ConsultationMessageType,
    example: ConsultationMessageType.TEXT,
  })
  @IsEnum(ConsultationMessageType)
  @IsNotEmpty()
  messageType: ConsultationMessageType;

  @ApiProperty({
    description: 'Nội dung tin nhắn văn bản (bắt buộc cho TEXT)',
    example: 'Thông báo khuyến mãi đặc biệt dành cho bạn!',
    required: false,
  })
  @ValidateIf(o => o.messageType === ConsultationMessageType.TEXT)
  @IsString()
  @IsNotEmpty()
  text?: string;

  @ApiProperty({
    description: 'URL của hình ảnh (bắt buộc cho IMAGE)',
    example: 'https://example.com/promotion.jpg',
    required: false,
  })
  @ValidateIf(o => o.messageType === ConsultationMessageType.IMAGE)
  @IsString()
  @IsNotEmpty()
  imageUrl?: string;

  @ApiProperty({
    description: 'Nội dung tin nhắn kèm theo hình ảnh (tùy chọn cho IMAGE)',
    example: 'Khuyến mãi lớn - Giảm giá 50%',
    required: false,
  })
  @ValidateIf(o => o.messageType === ConsultationMessageType.IMAGE)
  @IsString()
  @IsOptional()
  imageMessage?: string;

  @ApiProperty({
    description: 'ID của sticker (bắt buộc cho STICKER)',
    example: 'sticker_promo',
    required: false,
  })
  @ValidateIf(o => o.messageType === ConsultationMessageType.STICKER)
  @IsString()
  @IsNotEmpty()
  stickerId?: string;

  @ApiProperty({
    description: 'Nội dung tin nhắn kèm theo sticker (tùy chọn cho STICKER)',
    example: 'Chúc bạn một ngày tốt lành!',
    required: false,
  })
  @ValidateIf(o => o.messageType === ConsultationMessageType.STICKER)
  @IsString()
  @IsOptional()
  stickerMessage?: string;

  @ApiProperty({
    description: 'URL của file đính kèm (bắt buộc cho FILE)',
    example: 'https://example.com/document.pdf',
    required: false,
  })
  @ValidateIf(o => o.messageType === ConsultationMessageType.FILE)
  @IsString()
  @IsNotEmpty()
  fileUrl?: string;

  @ApiProperty({
    description: 'Tên file đính kèm (bắt buộc cho FILE)',
    example: 'document.pdf',
    required: false,
  })
  @ValidateIf(o => o.messageType === ConsultationMessageType.FILE)
  @IsString()
  @IsNotEmpty()
  filename?: string;

  @ApiProperty({
    description: 'Nội dung tin nhắn kèm theo file (tùy chọn cho FILE)',
    example: 'Đây là tài liệu hướng dẫn sử dụng sản phẩm',
    required: false,
  })
  @ValidateIf(o => o.messageType === ConsultationMessageType.FILE)
  @IsString()
  @IsOptional()
  fileMessage?: string;
}

/**
 * DTO cho phản hồi gửi tin nhắn tư vấn hàng loạt
 */
export class SendBulkConsultationMessageResponseDto {
  @ApiProperty({
    description: 'Tổng số tin nhắn được gửi',
    example: 3,
  })
  totalSent: number;

  @ApiProperty({
    description: 'Số tin nhắn gửi thành công',
    example: 2,
  })
  successCount: number;

  @ApiProperty({
    description: 'Số tin nhắn gửi thất bại',
    example: 1,
  })
  failureCount: number;

  @ApiProperty({
    description: 'Chi tiết kết quả gửi tin nhắn',
    example: [
      { userId: '123456789', messageId: 'msg_001', status: 'sent' },
      { userId: '987654321', messageId: 'msg_002', status: 'sent' },
      { userId: '456789123', messageId: null, status: 'failed', error: 'User not found' }
    ],
    type: 'array',
  })
  results: Array<{
    userId: string;
    messageId?: string;
    status: 'sent' | 'failed';
    error?: string;
  }>;
}
