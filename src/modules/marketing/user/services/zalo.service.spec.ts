import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { ZaloService } from './zalo.service';
import { AppException, ErrorCode } from '@common/exceptions';

describe('ZaloService - generateOAuthUrl', () => {
  let service: ZaloService;
  let configService: jest.Mocked<ConfigService>;

  beforeEach(async () => {
    const mockConfigService = {
      get: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ZaloService,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<ZaloService>(ZaloService);
    configService = module.get(ConfigService);
  });

  describe('generateOAuthUrl', () => {
    beforeEach(() => {
      // Mock ZALO_APP_ID
      configService.get.mockImplementation((key: string) => {
        if (key === 'ZALO_APP_ID') return 'test_app_id_123';
        if (key === 'ZALO_REDIRECT_URI') return 'https://v2.redai.vn/integration/zalo/oa';
        return undefined;
      });
    });

    it('should generate OAuth URL with auto-generated state having zalo_integration_oa prefix', async () => {
      const result = await service.generateOAuthUrl();

      expect(result).toHaveProperty('oauthUrl');
      expect(result).toHaveProperty('state');
      
      // Kiểm tra state có tiền tố đúng
      expect(result.state).toMatch(/^zalo_integration_oa_\d+_[a-z0-9]+$/);
      
      // Kiểm tra URL có chứa các tham số cần thiết
      expect(result.oauthUrl).toContain('https://oauth.zaloapp.com/v4/oa/permission');
      expect(result.oauthUrl).toContain('app_id=test_app_id_123');
      expect(result.oauthUrl).toContain('redirect_uri=');
      expect(result.oauthUrl).toContain(`state=${result.state}`);
      expect(result.oauthUrl).toContain('nocache=');
    });

    it('should generate OAuth URL with custom redirect URI', async () => {
      const customRedirectUri = 'https://custom.domain.com/callback';
      
      const result = await service.generateOAuthUrl(customRedirectUri);

      expect(result.oauthUrl).toContain(`redirect_uri=${encodeURIComponent(customRedirectUri)}`);
      expect(result.state).toMatch(/^zalo_integration_oa_\d+_[a-z0-9]+$/);
    });

    it('should use default redirect URI when not provided', async () => {
      const result = await service.generateOAuthUrl();

      expect(result.oauthUrl).toContain('redirect_uri=https%3A%2F%2Fv2.redai.vn%2Fintegration%2Fzalo%2Foa');
    });

    it('should generate different states for multiple calls', async () => {
      const result1 = await service.generateOAuthUrl();
      const result2 = await service.generateOAuthUrl();

      expect(result1.state).not.toEqual(result2.state);
      expect(result1.state).toMatch(/^zalo_integration_oa_\d+_[a-z0-9]+$/);
      expect(result2.state).toMatch(/^zalo_integration_oa_\d+_[a-z0-9]+$/);
    });

    it('should throw AppException when ZALO_APP_ID is not configured', async () => {
      configService.get.mockImplementation((key: string) => {
        if (key === 'ZALO_APP_ID') return undefined;
        return undefined;
      });

      await expect(service.generateOAuthUrl()).rejects.toThrow(AppException);
      await expect(service.generateOAuthUrl()).rejects.toThrow('ZALO_APP_ID không được định nghĩa');
    });

    it('should include nocache parameter to prevent caching', async () => {
      const result = await service.generateOAuthUrl();

      expect(result.oauthUrl).toMatch(/nocache=\d+/);
    });

    it('should generate state with timestamp and random string', async () => {
      const beforeTimestamp = Date.now();
      const result = await service.generateOAuthUrl();
      const afterTimestamp = Date.now();

      // Extract timestamp from state
      const stateMatch = result.state.match(/^zalo_integration_oa_(\d+)_([a-z0-9]+)$/);
      expect(stateMatch).not.toBeNull();
      
      const [, timestampStr, randomStr] = stateMatch!;
      const timestamp = parseInt(timestampStr, 10);
      
      // Kiểm tra timestamp nằm trong khoảng thời gian test
      expect(timestamp).toBeGreaterThanOrEqual(beforeTimestamp);
      expect(timestamp).toBeLessThanOrEqual(afterTimestamp);
      
      // Kiểm tra random string có độ dài phù hợp
      expect(randomStr).toHaveLength(13);
      expect(randomStr).toMatch(/^[a-z0-9]+$/);
    });
  });
});
