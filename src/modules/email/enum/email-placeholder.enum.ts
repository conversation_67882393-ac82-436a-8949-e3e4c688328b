// File: F:\Redon\DuAn\project01\project\backend\redai-v201-be-app\src\modules\email\shared\email-placeholder.enum.ts

/**
 * Enum chứa các placeholder cho email thông báo đánh giá ticket dưới 3 sao
 */
export enum TicketLowRatingPlaceholder {
  EMAIL = 'EMAIL',
  EMPLOYEE_NAME = 'EMPLOYEE_NAME',
}

/**
 * Enum chứa các placeholder cho email yêu cầu rút tiền Affiliate bị từ chối
 */
export enum AffiliateWithdrawRejectedPlaceholder {
  EMAIL = 'EMAIL',
  WITHDRAW_ID = 'WITHDRAW_ID',
  NAME = 'NAME',
  AFFILIATE_ACCOUNT_TYPE = 'AFFILIATE_ACCOUNT_TYPE',
  WITH_DRAW_STATUS = 'WITH_DRAW_STATUS',
  USER_ID = 'USER_ID',
  WITH_DRAW_REQUEST_DATE = 'WITH_DRAW_REQUEST_DATE',
  WITHDRAW_TOTAl = 'WITHDRAW_TOTAl',
  WITH_DRAW_REASON = 'WITH_DRAW_REASON',
}

/**
 * Enum chứa các placeholder cho email cảm ơn đề xuất tính năng
 */
export enum FeatureSuggestionThankYouPlaceholder {
  EMAIL = 'EMAIL',
  NAME = 'NAME',
  PHONE = 'PHONE',
  FEATURE_REQUIRE_ID = 'FEATURE_REQUIRE_ID',
  FEATURE_REQUIRE_CREATED_AT = 'FEATURE_REQUIRE_CREATED_AT',
  FEATURE_REQUIRE_CONTENT = 'FEATURE_REQUIRE_CONTENT',
}

/**
 * Enum chứa các placeholder cho email thông báo giảm giá
 */
export enum DiscountCampaignPlaceholder {
  EMAIL = 'EMAIL',
  NAME = 'NAME',
}

/**
 * Enum chứa các placeholder cho email trợ lý chưa được bật quá 3 ngày
 */
export enum AssistantInactivePlaceholder {
  EMAIL = 'EMAIL',
  NAME = 'NAME',
}

/**
 * Enum chứa các placeholder cho email đề xuất tính năng mới của nhân viên
 */
export enum EmployeeFeatureSuggestionPlaceholder {
  EMAIL = 'EMAIL',
  EMPLOYEE_NAME = 'EMPLOYEE_NAME',
  USER_ID = 'USER_ID',
}

/**
 * Enum chứa các placeholder cho email tích hợp cổng thanh toán
 */
export enum PaymentGatewayIntegrationPlaceholder {
  EMAIL = 'EMAIL',
  NAME = 'NAME',
  USER_ID = 'USER_ID',
  BANK_LOGO = 'BANK_LOGO',
  BANK_NAME = 'BANK_NAME',
  BANK_BRANCH = 'BANK_BRANCH',
  BANK_ACCOUNT_NAME = 'BANK_ACCOUNT_NAME',
  BANK_ACCOUNT_NUMBER = 'BANK_ACCOUNT_NUMBER',
}

/**
 * Enum chứa các placeholder cho email tích hợp website
 */
export enum WebsiteIntegrationPlaceholder {
  EMAIL = 'EMAIL',
  NAME = 'NAME',
  USER_ID = 'USER_ID',
}

/**
 * Enum chứa các placeholder cho email xin lỗi vì trải nghiệm tồi
 */
export enum BadExperienceApologyPlaceholder {
  EMAIL = 'EMAIL',
  NAME = 'NAME',
}

/**
 * Enum chứa các placeholder cho email nhắc ký hợp đồng nguyên tắc
 */
export enum RuleContractReminderPlaceholder {
  EMAIL = 'EMAIL',
  NAME = 'NAME',
  USER_ID = 'USER_ID',
}

/**
 * Enum chứa các placeholder cho email quyền lợi ưu tiên đề xuất của đối tác
 */
export enum AffiliatePriorityBenefitPlaceholder {
  EMAIL = 'EMAIL',
  USER_ID = 'USER_ID',
  NAME = 'NAME',
}

/**
 * Enum chứa các placeholder cho email tặng function và chiến lược khi tăng Rank
 */
export enum AffiliateRankUpRewardPlaceholder {
  EMAIL = 'EMAIL',
  NAME = 'NAME',
  USER_ID = 'USER_ID',
}

/**
 * Enum chứa các placeholder cho email tích hợp Facebook
 */
export enum FacebookIntegrationPlaceholder {
  EMAIL = 'EMAIL',
  NAME = 'NAME',
  USER_ID = 'USER_ID',
  PAGE_NAME = 'PAGE_NAME',
}

/**
 * Enum chứa các placeholder cho email hợp đồng nguyên tắc mới cần xử lý
 */
export enum NewRuleContractProcessingPlaceholder {
  EMAIL = 'EMAIL',
  RULE_CONTRACT_ID = 'RULE_CONTRACT_ID',
  EMPLOYEE_NAME = 'EMPLOYEE_NAME',
  NAME = 'NAME',
  USER_ID = 'USER_ID',
}

/**
 * Enum chứa các placeholder cho email sửa thông tin doanh nghiệp cần xử lý
 */
export enum BusinessInfoUpdateProcessingPlaceholder {
  EMAIL = 'EMAIL',
  USER_ID = 'USER_ID',
  EMPLOYEE_NAME = 'EMPLOYEE_NAME',
  USER_NAME = 'USER_NAME',
  BUSINESS_UPDATED = 'BUSINESS_UPDATED',
  BUSINESS_NAME = 'BUSINESS_NAME',
  BUSINESS_TAX_CODE = 'BUSINESS_TAX_CODE',
  BUSINESS_REPRESENTATIVE_NAME = 'BUSINESS_REPRESENTATIVE_NAME',
  BUSINESS_REPRESENTATIVE_POSITION = 'BUSINESS_REPRESENTATIVE_POSITION',
  BUSINESS_ADDRESS = 'BUSINESS_ADDRESS',
  BUSINESS_PHONE = 'BUSINESS_PHONE',
  BUSINESS_EMAIL = 'BUSINESS_EMAIL',
  DEADLINE_DATE = 'DEADLINE_DATE',
}

/**
 * Enum chứa các placeholder cho email ký hợp đồng nguyên tắc thất bại
 */
export enum RuleContractSignFailedPlaceholder {
  EMAIL = 'EMAIL',
  RULE_CONTRACT_ID = 'RULE_CONTRACT_ID',
  NAME = 'NAME',
  RULE_REASON = 'RULE_REASON',
}

/**
 * Enum chứa các placeholder cho email đăng ký đối tác đang được xử lý
 */
export enum AffiliateRegistrationProcessingPlaceholder {
  EMAIL = 'EMAIL',
  NAME = 'NAME',
  USER_ID = 'USER_ID',
}

/**
 * Enum chứa các placeholder cho email khiếu nại mới
 */
export enum NewComplaintPlaceholder {
  EMAIL = 'EMAIL',
  USER_ID = 'USER_ID',
  EMPLOYEE_NAME = 'EMPLOYEE_NAME',
}

/**
 * Enum chứa các placeholder cho email hóa đơn mới cần xuất
 */
export enum NewInvoiceProcessingPlaceholder {
  EMAIL = 'EMAIL',
  EMPLOYEE_NAME = 'EMPLOYEE_NAME',
  USER_ID = 'USER_ID',
  RPOINT_NAME = 'RPOINT_NAME',
  ORDER_POINT = 'ORDER_POINT',
  RPOINT_PRICE = 'RPOINT_PRICE',
  VND_COUPON_CODE = 'VND_COUPON_CODE',
  VND_COUPON_TOTAL = 'VND_COUPON_TOTAL',
  ORDER_TOTAL = 'ORDER_TOTAL',
  ORDER_STATUS = 'ORDER_STATUS',
}

/**
 * Enum chứa các placeholder cho email ký hợp đồng cộng tác viên thành công
 */
export enum AffiliateContractSignedPlaceholder {
  EMAIL = 'EMAIL',
  NAME = 'NAME',
}

/**
 * Enum chứa các placeholder cho email import khách hàng thành công
 */
export enum CustomerImportSuccessPlaceholder {
  EMAIL = 'EMAIL',
  NAME = 'NAME',
}

/**
 * Enum chứa các placeholder cho email đăng ký đối tác bị từ chối
 */
export enum AffiliateRegistrationRejectedPlaceholder {
  EMAIL = 'EMAIL',
  NAME = 'NAME',
  USER_ID = 'USER_ID',
  AFFILIATE_REASON = 'AFFILIATE_REASON',
}

/**
 * Enum chứa các placeholder cho email tặng mã giảm giá
 */
export enum AffiliateDiscountCodePlaceholder {
  EMAIL = 'EMAIL',
  USER_ID = 'USER_ID',
  NAME = 'NAME',
}

/**
 * Enum chứa các placeholder cho email yêu cầu rút tiền Affiliate đã được xử lý (Cá nhân)
 */
export enum AffiliateWithdrawProcessedIndividualPlaceholder {
  EMAIL = 'EMAIL',
  WITHDRAW_ID = 'WITHDRAW_ID',
  NAME = 'NAME',
  WITH_DRAW_REQUEST_DATE = 'WITH_DRAW_REQUEST_DATE',
  WITH_DRAW_FINISH_DATE = 'WITH_DRAW_FINISH_DATE',
  THUE_TNCN = 'THUE_TNCN',
  WITH_DRAW_STATUS = 'WITH_DRAW_STATUS',
  BANK_LOGO = 'BANK_LOGO',
  BANK_NAME = 'BANK_NAME',
  BANK_BRANCH = 'BANK_BRANCH',
  BANK_ACCOUNT_NAME = 'BANK_ACCOUNT_NAME',
  BANK_ACCOUNT_NUMBER = 'BANK_ACCOUNT_NUMBER',
}

/**
 * Enum chứa các placeholder cho email yêu cầu rút tiền Affiliate đã được xử lý (Doanh nghiệp)
 */
export enum AffiliateWithdrawProcessedBusinessPlaceholder {
  EMAIL = 'EMAIL',
  WITHDRAW_ID = 'WITHDRAW_ID',
  NAME = 'NAME',
  WITH_DRAW_REQUEST_DATE = 'WITH_DRAW_REQUEST_DATE',
  WITH_DRAW_FINISH_DATE = 'WITH_DRAW_FINISH_DATE',
  WITH_DRAW_STATUS = 'WITH_DRAW_STATUS',
  BANK_LOGO = 'BANK_LOGO',
  BANK_NAME = 'BANK_NAME',
  BANK_BRANCH = 'BANK_BRANCH',
  BANK_ACCOUNT_NAME = 'BANK_ACCOUNT_NAME',
  BANK_ACCOUNT_NUMBER = 'BANK_ACCOUNT_NUMBER',
}

/**
 * Enum chứa các placeholder cho email nâng cấp và bảo trì hệ thống
 */
export enum SystemMaintenancePlaceholder {
  EMAIL = 'EMAIL',
  NAME = 'NAME',
}

/**
 * Enum chứa các placeholder cho email xóa cổng thanh toán
 */
export enum PaymentGatewayDeletionPlaceholder {
  EMAIL = 'EMAIL',
  USER_ID = 'USER_ID',
  NAME = 'NAME',
  BANK_LOGO = 'BANK_LOGO',
  BANK_NAME = 'BANK_NAME',
  BANK_BRANCH = 'BANK_BRANCH',
  BANK_ACCOUNT_NAME = 'BANK_ACCOUNT_NAME',
  BANK_ACCOUNT_NUMBER = 'BANK_ACCOUNT_NUMBER',
}

/**
 * Enum chứa các placeholder cho email kích hoạt lại tài khoản
 */
export enum AccountReactivationPlaceholder {
  TWO_FA_CODE = 'TWO_FA_CODE',
}

/**
 * Enum chứa các placeholder cho email xác minh 2 lớp của nhân viên
 */
export enum EmployeeTwoFAPlaceholder {
  EMAIL = 'EMAIL',
  EMPLOYEE_ID = 'EMPLOYEE_ID',
  EMPLOYEE_NAME = 'EMPLOYEE_NAME',
  TWO_FA_CODE = 'TWO_FA_CODE',
  EMPLOYEE_EMAIL = 'EMPLOYEE_EMAIL',
}

/**
 * Enum chứa các placeholder cho email sản phẩm vi phạm chính sách
 */
export enum PolicyViolationProductPlaceholder {
  EMAIL = 'EMAIL',
  NAME = 'NAME',
  USER_ID = 'USER_ID',
}

/**
 * Enum chứa các placeholder cho email xác minh email
 */
export enum EmailVerificationPlaceholder {
  EMAIL = 'EMAIL',
  USER_ID = 'USER_ID',
  NAME = 'NAME',
  TWO_FA_CODE = 'TWO_FA_CODE',
}

/**
 * Enum chứa các placeholder cho email xác nhận thay đổi thông tin doanh nghiệp
 */
export enum BusinessInfoChangeConfirmationPlaceholder {
  EMAIL = 'EMAIL',
  NAME = 'NAME',
  BUSINESS_NAME = 'BUSINESS_NAME',
  BUSINESS_TAX_CODE = 'BUSINESS_TAX_CODE',
  BUSINESS_REPRESENTATIVE_NAME = 'BUSINESS_REPRESENTATIVE_NAME',
  BUSINESS_REPRESENTATIVE_POSITION = 'BUSINESS_REPRESENTATIVE_POSITION',
  BUSINESS_ADDRESS = 'BUSINESS_ADDRESS',
  BUSINESS_PHONE = 'BUSINESS_PHONE',
  BUSINESS_EMAIL = 'BUSINESS_EMAIL',
}

/**
 * Enum chứa các placeholder cho email đăng ký đối tác thành công
 */
export enum AffiliateRegistrationSuccessPlaceholder {
  EMAIL = 'EMAIL',
  NAME = 'NAME',
  AFFILIATE_REFERRAL_LINK = 'AFFILIATE_REFERRAL_LINK',
  RANK_LOGO = 'RANK_LOGO',
  AFFILIATE_RANK_UPDATE_DATE = 'AFFILIATE_RANK_UPDATE_DATE',
  RANK_NAME = 'RANK_NAME',
  AFFILIATE_ACCOUNT_STATUS = 'AFFILIATE_ACCOUNT_STATUS',
  RANK_COMMISSION = 'RANK_COMMISSION',
  AFFILIATE_ACCOUNT_TYPE = 'AFFILIATE_ACCOUNT_TYPE',
  BANK_LOGO = 'BANK_LOGO',
  BANK_NAME = 'BANK_NAME',
  BANK_BRANCH = 'BANK_BRANCH',
  BANK_ACCOUNT_NAME = 'BANK_ACCOUNT_NAME',
  BANK_ACCOUNT_NUMBER = 'BANK_ACCOUNT_NUMBER',
}

/**
 * Enum chứa các placeholder cho email thanh toán thành công
 */
export enum PaymentSuccessPlaceholder {
  EMAIL = 'EMAIL',
  ORDER_ID = 'ORDER_ID',
  NAME = 'NAME',
  USER_ID = 'USER_ID',
  ADDRESS = 'ADDRESS',
  USER_NAME = 'USER_NAME',
  PHONE = 'PHONE',
  ORDER_CREATE_DATE = 'ORDER_CREATE_DATE',
  RPOINT_NAME = 'RPOINT_NAME',
  ORDER_POINT = 'ORDER_POINT',
  RPOINT_PRICE = 'RPOINT_PRICE',
  VND_COUPON_CODE = 'VND_COUPON_CODE',
  VND_COUPON_TOTAL = 'VND_COUPON_TOTAL',
  ORDER_TOTAL = 'ORDER_TOTAL',
  ORDER_STATUS = 'ORDER_STATUS',
}

/**
 * Enum chứa các placeholder cho email yêu cầu rút tiền Affiliate cần xử lý
 */
export enum AffiliateWithdrawProcessingPlaceholder {
  EMPLOYEE_EMAIL = 'EMPLOYEE_EMAIL',
  WITHDRAW_ID = 'WITHDRAW_ID',
  USER_ID = 'USER_ID',
  EMPLOYEE_NAME = 'EMPLOYEE_NAME',
  NAME = 'NAME',
  AFFLIATE_ACCOUNT_ID = 'AFFLIATE_ACCOUNT_ID',
  WITHDRAW_TOTAl = 'WITHDRAW_TOTAl',
  DATE_NOW = 'DATE_NOW',
  DEADLINE_DATE = 'DEADLINE_DATE',
}

/**
 * Enum chứa các placeholder cho email phát hành hóa đơn VAT
 */
export enum VatInvoiceIssuancePlaceholder {
  EMAIL = 'EMAIL',
  ORDER_ID = 'ORDER_ID',
  ORDER_POINT_NAME = 'ORDER_POINT_NAME',
  NAME = 'NAME',
  ORDER_CREATE_DATE = 'ORDER_CREATE_DATE',
  USER_NAME = 'USER_NAME',
  USER_ID = 'USER_ID',
  ORDER_POINT = 'ORDER_POINT',
  ORDER_TOTAL = 'ORDER_TOTAL',
  VND_COUPON_CODE = 'VND_COUPON_CODE',
  ORDER_VND_COUPON_TOTAL = 'ORDER_VND_COUPON_TOTAL',
  ORDER_STATUS = 'ORDER_STATUS',
}

/**
 * Enum chứa các placeholder cho email đăng ký tài khoản thành công
 */
export enum AccountRegistrationSuccessPlaceholder {
  EMAIL = 'EMAIL',
  NAME = 'NAME',
  USER_EMAIL = 'USER_EMAIL',
  USER_ID = 'USER_ID',
}

/**
 * Enum chứa các placeholder cho email hợp đồng cộng tác viên mới cần xử lý
 */
export enum NewAffiliateContractProcessingPlaceholder {
  EMAIL = 'EMAIL',
  AFFLIATE_ACCOUNT_ID = 'AFFLIATE_ACCOUNT_ID',
  NAME = 'NAME',
  EMPLOYEE_NAME = 'EMPLOYEE_NAME',
  AFFILIATE_SIGNED_DATE = 'AFFILIATE_SIGNED_DATE',
  AFFILIATE_ACCOUNT_TYPE = 'AFFILIATE_ACCOUNT_TYPE',
}

/**
 * Enum chứa các placeholder cho email ký hợp đồng nguyên tắc thành công
 */
export enum RuleContractSignedPlaceholder {
  EMAIL = 'EMAIL',
  RULE_CONTRACT_ID = 'RULE_CONTRACT_ID',
  NAME = 'NAME',
  RULE_CONTRACT_SIGNED_DATE = 'RULE_CONTRACT_SIGNED_DATE',
}

/**
 * Enum chứa các placeholder cho email hợp đồng nguyên tắc đang được xử lý
 */
export enum RuleContractProcessingPlaceholder {
  EMAIL = 'EMAIL',
  RULE_CONTRACT_ID = 'RULE_CONTRACT_ID',
  NAME = 'NAME',
  USER_ID = 'USER_ID',
}

/**
 * Enum chứa các placeholder cho email thông tin đăng nhập Admin
 */
export enum AdminLoginInfoPlaceholder {
  EMPLOYEE_EMAIL = 'EMPLOYEE_EMAIL',
  EMPLOYEE_NAME = 'EMPLOYEE_NAME',
  EMPLOYEE_ID = 'EMPLOYEE_ID',
  TWO_FA_CODE = 'TWO_FA_CODE',
  EMAIL = 'EMAIL',
}

/**
 * Enum chứa các placeholder cho email phản hồi đề xuất tính năng
 */
export enum FeatureSuggestionResponsePlaceholder {
  EMAIL = 'EMAIL',
  NAME = 'NAME',
  PHONE = 'PHONE',
}

/**
 * Enum chứa các placeholder cho email xác nhận tiếp nhận khiếu nại
 */
export enum ComplaintReceivedPlaceholder {
  EMAIL = 'EMAIL',
  NAME = 'NAME',
  PHONE = 'PHONE',
}

/**
 * Enum chứa các placeholder cho email thay đổi Rank Affiliate giảm
 */
export enum AffiliateRankDownPlaceholder {
  EMAIL = 'EMAIL',
  NAME = 'NAME',
  RANK_LOGO = 'RANK_LOGO',
  AFFILIATE_ACCOUNT_STATUS = 'AFFILIATE_ACCOUNT_STATUS',
  RANK_COMMISSION = 'RANK_COMMISSION',
  AFFILIATE_ACCOUNT_TYPE = 'AFFILIATE_ACCOUNT_TYPE',
  AFFILIATE_REFERRAL_LINK = 'AFFILIATE_REFERRAL_LINK',
}

/**
 * Enum chứa các placeholder cho email quyền lợi PR - Quảng cáo đối tác
 */
export enum AffiliatePRBenefitPlaceholder {
  EMAIL = 'EMAIL',
  NAME = 'NAME',
}

/**
 * Enum chứa các placeholder cho email gợi ý thanh toán giỏ hàng
 */
export enum CartPaymentReminderPlaceholder {
  EMAIL = 'EMAIL',
  NAME = 'NAME',
}

/**
 * Enum chứa các placeholder cho email tặng mã giảm giá độc quyền
 */
export enum ExclusiveDiscountCodePlaceholder {
  EMAIL = 'EMAIL',
  NAME = 'NAME',
}

/**
 * Enum chứa các placeholder cho email đổi thông tin ngân hàng thành công
 */
export enum BankInfoChangeSuccessPlaceholder {
  EMAIL = 'EMAIL',
  NAME = 'NAME',
  BANK_LOGO = 'BANK_LOGO',
  BANK_NAME = 'BANK_NAME',
  BANK_BRANCH = 'BANK_BRANCH',
  BANK_ACCOUNT_NAME = 'BANK_ACCOUNT_NAME',
  BANK_ACCOUNT_NUMBER = 'BANK_ACCOUNT_NUMBER',
}

/**
 * Enum chứa các placeholder cho email sửa thông tin ngân hàng cần xử lý
 */
export enum BankInfoUpdateProcessingPlaceholder {
  EMAIL = 'EMAIL',
  USER_ID = 'USER_ID',
  EMPLOYEE_NAME = 'EMPLOYEE_NAME',
  BANK_LOGO = 'BANK_LOGO',
  BANK_NAME = 'BANK_NAME',
  BANK_BRANCH = 'BANK_BRANCH',
  BANK_ACCOUNT_NAME = 'BANK_ACCOUNT_NAME',
  BANK_ACCOUNT_NUMBER = 'BANK_ACCOUNT_NUMBER',
}

/**
 * Enum chứa các placeholder cho email số dư tài khoản thấp
 */
export enum LowBalancePlaceholder {
  EMAIL = 'EMAIL',
  NAME = 'NAME',
  USER_ID = 'USER_ID',
}

/**
 * Enum chứa các placeholder cho email tài khoản lâu không hoạt động
 */
export enum InactiveAccountPlaceholder {
  EMAIL = 'EMAIL',
  NAME = 'NAME',
  USER_ID = 'USER_ID',
}

/**
 * Enum chứa các placeholder cho email cập nhật tính năng mới
 */
export enum NewFeatureUpdatePlaceholder {
  EMAIL = 'EMAIL',
  NAME = 'NAME',
}

/**
 * Enum chứa các placeholder cho email xác minh 2 lớp
 */
export enum TwoFAPlaceholder {
  EMAIL = 'EMAIL',
  USER_ID = 'USER_ID',
  NAME = 'NAME',
  TWO_FA_CODE = 'TWO_FA_CODE',
}

/**
 * Enum chứa các placeholder cho email xóa tài nguyên vi phạm
 */
export enum ResourceViolationDeletionPlaceholder {
  EMAIL = 'EMAIL',
  NAME = 'NAME',
  USER_ID = 'USER_ID',
}

/**
 * Enum chứa các placeholder cho email tích hợp Zalo
 */
export enum ZaloIntegrationPlaceholder {
  EMAIL = 'EMAIL',
  NAME = 'NAME',
  USER_ID = 'USER_ID',
}

/**
 * Enum chứa các placeholder cho email thông báo bài viết chất lượng
 */
export enum QualityPostNotificationPlaceholder {
  EMAIL = 'EMAIL',
  NAME = 'NAME',
}

/**
 * Enum chứa các placeholder cho email nhắc nhở đánh giá ticket
 */
export enum TicketRatingReminderPlaceholder {
  EMAIL = 'EMAIL',
  USER_ID = 'USER_ID',
  NAME = 'NAME',
}

/**
 * Enum chứa các placeholder cho email quên mật khẩu
 */
export enum ForgotPasswordPlaceholder {
  EMAIL = 'EMAIL',
  NAME = 'NAME',
  NEW_PASSWORD = 'NEW_PASSWORD',
  USER_ID = 'USER_ID',
}

/**
 * Enum chứa các placeholder cho email khắc phục sự cố thành công
 */
export enum IssueResolvedPlaceholder {
  EMAIL = 'EMAIL',
  NAME = 'NAME',
  USER_ID = 'USER_ID',
}

/**
 * Enum chứa các placeholder cho email import khách hàng thất bại
 */
export enum CustomerImportFailedPlaceholder {
  EMAIL = 'EMAIL',
  NAME = 'NAME',
}

/**
 * Enum chứa các placeholder cho email import thông tin sản phẩm thành công
 */
export enum ProductImportSuccessPlaceholder {
  EMAIL = 'EMAIL',
  NAME = 'NAME',
}

/**
 * Enum chứa các placeholder cho email import thông tin sản phẩm thất bại
 */
export enum ProductImportFailedPlaceholder {
  EMAIL = 'EMAIL',
  NAME = 'NAME',
}

/**
 * Enum chứa các placeholder cho email cảm ơn đánh giá 5 sao
 */
export enum TicketFiveStarRatingPlaceholder {
  EMAIL = 'EMAIL',
  NAME = 'NAME',
  TIKET_ID = 'TIKET_ID',
}

/**
 * Enum chứa các placeholder cho email phát sinh sự cố
 */
export enum SystemIssuePlaceholder {
  EMAIL = 'EMAIL',
  NAME = 'NAME',
}

/**
 * Enum chứa các placeholder cho email quên mật khẩu của nhân viên
 */
export enum EmployeeForgotPasswordPlaceholder {
  EMAIL = 'EMAIL',
  USER_ID = 'USER_ID',
  NAME = 'NAME',
  EMPLOYEE_NAME = 'EMPLOYEE_NAME',
  EMPLOYEE_ID = 'EMPLOYEE_ID',
  TWO_FA_CODE = 'TWO_FA_CODE',
}

/**
 * Enum chứa các placeholder cho email tặng khóa học AI Automation
 */
export enum AIAutomationCourseGiftPlaceholder {
  EMAIL = 'EMAIL',
  NAME = 'NAME',
}

/**
 * Enum chứa các placeholder cho email thay đổi Rank Affiliate tăng
 */
export enum AffiliateRankUpPlaceholder {
  EMAIL = 'EMAIL',
  NAME = 'NAME',
  RANK_LOGO = 'RANK_LOGO',
  AFFILIATE_ACCOUNT_STATUS = 'AFFILIATE_ACCOUNT_STATUS',
  RANK_COMMISSION = 'RANK_COMMISSION',
  AFFILIATE_ACCOUNT_TYPE = 'AFFILIATE_ACCOUNT_TYPE',
  USER_ID = 'USER_ID',
  AFFILIATE_REFERRAL_LINK = 'AFFILIATE_REFERRAL_LINK',
}

/**
 * Enum chứa các placeholder cho email mở khóa kho bài viết AI Premium
 */
export enum AIPremiumContentUnlockPlaceholder {
  EMAIL = 'EMAIL',
  NAME = 'NAME',
}

/**
 * Enum chứa các placeholder cho email ký hợp đồng nguyên tắc OTP
 */
export enum RuleContractOTPSigningPlaceholder {
  EMAIL = 'EMAIL',
  USER_ID = 'USER_ID',
  NAME = 'NAME',
  TWO_FA_CODE = 'TWO_FA_CODE',
}

/**
 * Enum chứa các placeholder cho email tạm ngưng tài khoản
 */
export enum AccountSuspensionPlaceholder {
  EMAIL = 'EMAIL',
  USER_ID = 'USER_ID',
  NAME = 'NAME',
  DATE_NOW = 'DATE_NOW',
  USER_REASON_DISABLE = 'USER_REASON_DISABLE',
}

/**
 * Enum chứa các placeholder cho email ký hợp đồng cộng tác viên OTP
 */
export enum AffiliateContractOTPSigningPlaceholder {
  EMAIL = 'EMAIL',
  USER_ID = 'USER_ID',
  NAME = 'NAME',
  TWO_FA_CODE = 'TWO_FA_CODE',
}
