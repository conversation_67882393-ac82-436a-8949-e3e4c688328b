import { AppException, ErrorCode } from '@common/exceptions';
import { ApiResponseDto, PaginatedResult } from '@common/response';
import { EmployeeInfoService } from '@modules/employee/services/employee-info.service';
import { Injectable, Logger } from '@nestjs/common';
import { AiProviderHelper } from '@shared/services/ai/helpers/ai-provider.helper';
import { Transactional } from 'typeorm-transactional';
import { MODELS_ERROR_CODES } from '../../exceptions';
import { ApiKeyEncryptionHelper } from '../../helpers/api-key-encryption.helper';
import { SystemKeyLlmRepository } from '../../repositories/system-key-llm.repository';
import { SystemModelSyncService } from '../../services/system-model-sync.service';
import { ModelDiscoveryInfo } from '../../interfaces/model-discovery-info.interface';
import {
  CreateSystem<PERSON>eyLlmDto,
  CreateSystemKeyLlmResponseDto,
  UpdateSystemKeyLlmResponseDto,
  RestoreSystemKeyLlmResponseDto,
  SystemKeyLlmDetailResponseDto,
  SystemKeyLlmQueryDto,
  SystemKeyLlmResponseDto,
  TestConnectionResponseDto,
  UpdateSystemKeyLlmDto
} from '../dto/system-key-llm';
import { SystemKeyLlmMapper } from '../mappers/system-key-llm.mapper';

/**
 * Service xử lý business logic cho Admin System Key LLM
 */
@Injectable()
export class AdminSystemKeyLlmService {
  private readonly logger = new Logger(AdminSystemKeyLlmService.name);

  constructor(
    private readonly systemKeyLlmRepository: SystemKeyLlmRepository,
    private readonly apiKeyEncryptionHelper: ApiKeyEncryptionHelper,
    private readonly aiProviderHelper: AiProviderHelper,
    private readonly employeeInfoService: EmployeeInfoService,
    private readonly aiProviderService: AiProviderHelper,
    private readonly systemModelSyncService: SystemModelSyncService,
  ) { }

  /**
   * Tạo mới system key LLM với model auto-discovery
   */
  @Transactional()
  async create(createDto: CreateSystemKeyLlmDto, employeeId: number): Promise<ApiResponseDto<CreateSystemKeyLlmResponseDto>> {
    this.logger.log(`Creating system key LLM by employee ${employeeId}`);

    // Kiểm tra trùng tên
    const existsByName = await this.systemKeyLlmRepository.existsByName(createDto.name);
    if (existsByName) {
      throw new AppException(MODELS_ERROR_CODES.SYSTEM_KEY_LLM_NAME_EXISTS);
    }

    // Test connection trước khi lưu
    const encryptedApiKey = this.apiKeyEncryptionHelper.encryptAdminApiKey(createDto.apiKey);

    // Test connection với provider
    const testResult = await this.aiProviderService.testConnection(
      encryptedApiKey,
      createDto.provider,
      true,
    );

    if (!testResult.success) {
      throw new AppException(MODELS_ERROR_CODES.USER_KEY_LLM_CONNECTION_FAILED, testResult.error);
    }

    // Tạo entity mới
    const newSystemKey = this.systemKeyLlmRepository.create({
      name: createDto.name,
      provider: createDto.provider,
      apiKey: encryptedApiKey,
      createdAt: Date.now(),
      createdBy: employeeId,
      updatedAt: Date.now(),
      updatedBy: employeeId
    });

    // Lưu vào database
    const savedKey = await this.systemKeyLlmRepository.save(newSystemKey);

    // Thực hiện model auto-discovery
    let modelDiscovery: ModelDiscoveryInfo | undefined;
    try {
      this.logger.log(`Starting model discovery for system key ${savedKey.id}`);

      const syncResult = await this.systemModelSyncService.syncModels({
        keyId: savedKey.id,
        provider: createDto.provider,
        encryptedApiKey: encryptedApiKey
      });

      modelDiscovery = {
        totalModelsFound: syncResult.discoveryResult.totalModelsFound,
        modelsMatched: syncResult.discoveryResult.modelsMatched,
        newModelsCreated: syncResult.discoveryResult.newModelsCreated,
        existingModelsFound: syncResult.discoveryResult.existingModelsFound,
        mappingsCreated: syncResult.discoveryResult.mappingsCreated,
        discoveryTime: syncResult.syncedAt,
        success: syncResult.success,
        message: syncResult.message,
        errors: syncResult.discoveryResult.errors
      };

      this.logger.log(`Model discovery completed for system key ${savedKey.id}: ${syncResult.message}`);
    } catch (error) {
      this.logger.error(`Model discovery failed for system key ${savedKey.id}: ${error.message}`, error.stack);

      modelDiscovery = {
        totalModelsFound: 0,
        modelsMatched: 0,
        newModelsCreated: 0,
        existingModelsFound: 0,
        mappingsCreated: 0,
        discoveryTime: Date.now(),
        success: false,
        message: `Model discovery thất bại: ${error.message}`,
        errors: [error.message]
      };
    }

    this.logger.log(`Created system key LLM ${savedKey.id} successfully`);

    const response: CreateSystemKeyLlmResponseDto = {
      id: savedKey.id,
      connectionError: testResult.error,
      modelDiscovery
    };

    return ApiResponseDto.success(response);
  }

  /**
   * Lấy danh sách system key LLM có phân trang với đầy đủ thông tin chi tiết
   */
  async findAll(queryDto: SystemKeyLlmQueryDto): Promise<ApiResponseDto<PaginatedResult<SystemKeyLlmDetailResponseDto>>> {
    this.logger.log('Getting system key LLM list with full details');

    // Lấy dữ liệu từ repository
    const result = await this.systemKeyLlmRepository.findWithPagination(queryDto);

    // Thu thập tất cả employee IDs từ các system keys
    const employeeIds: Set<number> = new Set();
    result.items.forEach(systemKey => {
      if (systemKey.createdBy) employeeIds.add(systemKey.createdBy);
      if (systemKey.updatedBy) employeeIds.add(systemKey.updatedBy);
      if (systemKey.deletedBy) employeeIds.add(systemKey.deletedBy);
    });

    // Lấy thông tin employee một lần cho tất cả IDs
    const employeeInfoMap = await this.employeeInfoService.getEmployeeInfoMap(Array.from(employeeIds));

    // Convert sang DTO với đầy đủ thông tin employee
    const items = result.items.map(systemKey => {
      const createdByInfo = systemKey.createdBy ? employeeInfoMap.get(systemKey.createdBy) || null : null;
      const updatedByInfo = systemKey.updatedBy ? employeeInfoMap.get(systemKey.updatedBy) || null : null;

      return SystemKeyLlmMapper.toDetailResponseDto(systemKey, createdByInfo, updatedByInfo);
    });

    return ApiResponseDto.paginated({
      items,
      meta: result.meta
    });
  }

  /**
   * Cập nhật system key LLM với model auto-discovery (nếu có thay đổi API key)
   */
  @Transactional()
  async update(id: string, updateDto: UpdateSystemKeyLlmDto, employeeId: number): Promise<ApiResponseDto<UpdateSystemKeyLlmResponseDto>> {

    // Tìm system key hiện tại
    const existingKey = await this.systemKeyLlmRepository.findByIdWithFullData(id);
    if (!existingKey) {
      throw new AppException(MODELS_ERROR_CODES.SYSTEM_KEY_LLM_NOT_FOUND);
    }

    // Kiểm tra trùng tên (nếu có thay đổi tên)
    if (updateDto.name && updateDto.name !== existingKey.name) {
      const existsByName = await this.systemKeyLlmRepository.existsByName(updateDto.name, id);
      if (existsByName) {
        throw new AppException(MODELS_ERROR_CODES.SYSTEM_KEY_LLM_NAME_EXISTS);
      }
    }

    // Cập nhật các trường
    if (updateDto.name !== undefined) {
      existingKey.name = updateDto.name;
    }

    let encryptedApiKey: string | undefined;
    let connectionError: string | undefined;
    let hasApiKeyChange = false;

    if (updateDto.apiKey !== undefined) {
      // Test connection
      try {
        const tempEncryptedKey = this.apiKeyEncryptionHelper.encryptAdminApiKey(updateDto.apiKey);
        hasApiKeyChange = true;

        // Test connection với provider
        const testResult = await this.aiProviderService.testConnection(
          tempEncryptedKey,
          existingKey.provider,
          true,
        );

        if (!testResult.success) {
          throw new AppException(MODELS_ERROR_CODES.USER_KEY_LLM_CONNECTION_FAILED, testResult.error);
        }

        existingKey.apiKey = tempEncryptedKey;
        encryptedApiKey = tempEncryptedKey;
        connectionError = testResult.error;
      } catch (error) {
        this.logger.error(`API key validation failed: ${error.message}`, error.stack);
        throw new AppException(MODELS_ERROR_CODES.SYSTEM_KEY_LLM_INVALID_KEY);
      }
    }

    existingKey.updatedBy = employeeId;
    existingKey.updatedAt = Date.now();

    // Lưu thay đổi
    await this.systemKeyLlmRepository.update(id, existingKey);

    // Thực hiện model auto-discovery nếu có thay đổi API key
    let modelDiscovery: ModelDiscoveryInfo | undefined;
    if (hasApiKeyChange && encryptedApiKey) {
      try {
        this.logger.log(`Starting model discovery for updated system key ${id}`);

        const syncResult = await this.systemModelSyncService.syncModels({
          keyId: id,
          provider: existingKey.provider,
          encryptedApiKey: encryptedApiKey
        });

        modelDiscovery = {
          totalModelsFound: syncResult.discoveryResult.totalModelsFound,
          modelsMatched: syncResult.discoveryResult.modelsMatched,
          newModelsCreated: syncResult.discoveryResult.newModelsCreated,
          existingModelsFound: syncResult.discoveryResult.existingModelsFound,
          mappingsCreated: syncResult.discoveryResult.mappingsCreated,
          discoveryTime: syncResult.syncedAt,
          success: syncResult.success,
          message: syncResult.message,
          errors: syncResult.discoveryResult.errors
        };

        this.logger.log(`Model discovery completed for updated system key ${id}: ${syncResult.message}`);
      } catch (error) {
        this.logger.error(`Model discovery failed for updated system key ${id}: ${error.message}`, error.stack);

        modelDiscovery = {
          totalModelsFound: 0,
          modelsMatched: 0,
          newModelsCreated: 0,
          existingModelsFound: 0,
          mappingsCreated: 0,
          discoveryTime: Date.now(),
          success: false,
          message: `Model discovery thất bại: ${error.message}`,
          errors: [error.message]
        };
      }
    }

    this.logger.log(`Updated system key LLM ${id} successfully`);

    const response: UpdateSystemKeyLlmResponseDto = {
      id: existingKey.id,
      connectionError,
      modelDiscovery
    };

    return ApiResponseDto.success(response);
  }

  /**
   * Xóa system key LLM (soft delete) và clear model mappings
   */
  @Transactional()
  async remove(id: string, employeeId: number): Promise<ApiResponseDto<{ id: string }>> {
    this.logger.log(`Soft deleting system key LLM ${id} by employee ${employeeId}`);

    // Kiểm tra system key tồn tại
    const existingKey = await this.systemKeyLlmRepository.findByIdWithFullData(id);
    if (!existingKey) {
      throw new AppException(MODELS_ERROR_CODES.SYSTEM_KEY_LLM_NOT_FOUND);
    }

    // Clear model mappings trước khi xóa key
    try {
      this.logger.log(`Clearing model mappings for system key ${id}`);
      const clearResult = await this.systemModelSyncService.clearModels(id);
      this.logger.log(`Cleared ${clearResult.deletedMappings} model mappings for system key ${id}`);
    } catch (error) {
      this.logger.error(`Failed to clear model mappings for system key ${id}: ${error.message}`, error.stack);
      // Không throw error, vẫn tiếp tục xóa key
    }

    // Thực hiện soft delete
    const deleted = await this.systemKeyLlmRepository.softDeleteSystemKey(id, employeeId);
    if (!deleted) {
      throw new AppException(MODELS_ERROR_CODES.SYSTEM_KEY_LLM_DELETE_FAILED);
    }

    this.logger.log(`Soft deleted system key LLM ${id} successfully`);
    return ApiResponseDto.success({ id });
  }

  /**
   * Lấy danh sách system key LLM đã xóa
   */
  async findDeleted(queryDto: SystemKeyLlmQueryDto): Promise<ApiResponseDto<PaginatedResult<SystemKeyLlmResponseDto>>> {
    this.logger.log('Getting deleted system key LLM list');

    // Lấy dữ liệu từ repository
    const result = await this.systemKeyLlmRepository.findDeletedWithPagination(queryDto);

    // Convert sang DTO đơn giản (chỉ id, name, provider)
    const items = SystemKeyLlmMapper.toResponseDtoArray(result.items);

    return ApiResponseDto.paginated({
      items,
      meta: result.meta
    });
  }

  /**
   * Khôi phục system key LLM đã xóa
   */
  @Transactional()
  async restore(ids: string[], employeeId: number): Promise<ApiResponseDto<RestoreSystemKeyLlmResponseDto>> {
    this.logger.log(`Restoring system key LLM ${ids.join(', ')} by employee ${employeeId}`);

    // Kiểm tra input
    if (!ids || ids.length === 0) {
      throw new AppException(MODELS_ERROR_CODES.SYSTEM_KEY_LLM_INVALID_KEY);
    }

    // Thực hiện khôi phục với kết quả chi tiết
    const result = await this.systemKeyLlmRepository.restoreWithDetails(ids);

    // Tạo response DTO
    const responseDto: RestoreSystemKeyLlmResponseDto = {
      restored: result.restored,
      failed: result.failed,
      totalRequested: ids.length,
      totalRestored: result.restored.length,
      totalFailed: result.failed.length
    };

    this.logger.log(`Restore completed: ${result.restored.length} restored, ${result.failed.length} failed`);
    return ApiResponseDto.success(responseDto);
  }

  /**
   * Test kết nối API key
   */
  async testConnection(id: string): Promise<ApiResponseDto<TestConnectionResponseDto>> {
    this.logger.log(`Testing connection for system key LLM ${id}`);

    // Tìm system key với encrypted API key
    const systemKey = await this.systemKeyLlmRepository.findByIdWithFullData(id);
    if (!systemKey) {
      throw new AppException(MODELS_ERROR_CODES.SYSTEM_KEY_LLM_NOT_FOUND);
    }

    try {
      // Test connection bằng cách lấy danh sách models
      const result = await this.aiProviderHelper.testConnection(
        systemKey.provider,
        systemKey.apiKey,
      );

      if (result.success) {
        const responseDto = SystemKeyLlmMapper.toTestConnectionSuccessDto();
        return ApiResponseDto.success(responseDto);
      } else {
        const responseDto = SystemKeyLlmMapper.toTestConnectionFailureDto(
          result.error || 'Kết nối thất bại'
        );
        return ApiResponseDto.success(responseDto);
      }
    } catch (error) {
      this.logger.error(`Test connection failed for key ${id}: ${error.message}`, error.stack);

      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, error.message);
    }
  }

  /**
   * Reload models từ system key LLM
   */
  async reloadModels(keyId: string): Promise<ApiResponseDto<any>> {
    this.logger.log(`Reloading models for system key ${keyId}`);

    try {
      const syncResult = await this.systemModelSyncService.reloadModels(keyId);

      const response = {
        keyId: syncResult.keyId,
        provider: syncResult.provider,
        modelDiscovery: {
          totalModelsFound: syncResult.discoveryResult.totalModelsFound,
          modelsMatched: syncResult.discoveryResult.modelsMatched,
          newModelsCreated: syncResult.discoveryResult.newModelsCreated,
          existingModelsFound: syncResult.discoveryResult.existingModelsFound,
          mappingsCreated: syncResult.discoveryResult.mappingsCreated,
          discoveryTime: syncResult.syncedAt,
          success: syncResult.success,
          message: syncResult.message,
          errors: syncResult.discoveryResult.errors
        }
      };

      return ApiResponseDto.success(response);
    } catch (error) {
      this.logger.error(`Failed to reload models for system key ${keyId}: ${error.message}`, error.stack);
      throw error;
    }
  }
}
