import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { HttpModule } from '@nestjs/axios';
import * as entities from '../entities';
import * as repositories from '../repositories';
import * as controllers from './controllers';
import * as services from './services';
import * as sharedServices from '../services';
import { HttpClientService } from '../services/http-client.service';
import { GHNValidationService } from '../services/providers/ghn-validation.service';
import { GHTKValidationService } from '../services/providers/ghtk-validation.service';
import { AhamoveValidationService } from '../services/providers/ahamove-validation.service';
import { JTValidationService } from '../services/providers/jt-validation.service';
import { SepayHubModule } from '@shared/services/sepay-hub';
import { Agent } from '@modules/agent/entities';
import { GoogleAdsAccount } from '@modules/marketing/user/entities/google-ads-account.entity';
import { GoogleApiModule } from '@shared/services/google';
import { AgentRepository } from '@modules/agent/repositories/agent.repository';
import { EncryptionService } from '@shared/services/encryption.service';

/**
 * Module quản lý tích hợp cho người dùng
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([...Object.values(entities), Agent, GoogleAdsAccount]),
    HttpModule,
    SepayHubModule,
    GoogleApiModule,
  ],
  controllers: Object.values(controllers),
  providers: [
    ...Object.values(repositories),
    ...Object.values(services),
    ...Object.values(sharedServices),
    HttpClientService,
    GHNValidationService,
    GHTKValidationService,
    AhamoveValidationService,
    JTValidationService,
    AgentRepository,
    GoogleAdsAccountRepository,
    EncryptionService,
  ],
  exports: [
    ...Object.values(services),
    HttpClientService,
    GHNValidationService,
    GHTKValidationService,
    AhamoveValidationService,
    JTValidationService,
  ],
})
export class IntegrationUserModule {}
