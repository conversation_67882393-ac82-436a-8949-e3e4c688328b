# Data Module Swagger Documentation

## Tổng quan

File `swagger.json` này chứa tài liệu API đầy đủ cho **Data Module** - module quản lý dữ liệu trong hệ thống RedAI, bao gồm:

- **Media Management** - Quản lý tệp đa phương tiện (hình ảnh, video, âm thanh)
- **URL Management** - Quản lý URL và crawling
- **Knowledge Files Management** - Quản lý tệp tri thức và vector stores
- **Statistics** - Thống kê dữ liệu và dung lượng lưu trữ

## Cấu trúc API

### 🔐 Authentication
Tất cả API đều yêu cầu JWT Bearer Token authentication:
```
Authorization: Bearer <your-jwt-token>
```

### 📊 API Tags

| Tag | Mô tả |
|-----|-------|
| `Admin Media` | Quản lý media cho admin |
| `User Media` | Quản lý media cho người dùng |
| `Admin URL` | Quản lý URL cho admin |
| `User URL` | Quản lý URL cho người dùng |
| `Admin Knowledge Files` | Quản lý file tri thức cho admin |
| `User Knowledge Files` | Quản lý file tri thức cho người dùng |
| `Admin Vector Store` | Quản lý vector store cho admin |
| `User Vector Store` | Quản lý vector store cho người dùng |
| `Admin Statistics` | Thống kê dữ liệu cho admin |
| `User Statistics` | Thống kê dữ liệu cho người dùng |

## 🎯 Endpoints chính

### Media Management

#### Admin Media
- `GET /admin/media` - Lấy danh sách media cho admin
- `GET /admin/media/{id}` - Lấy chi tiết media theo ID

#### User Media  
- `GET /media/my-media` - Lấy danh sách media của người dùng
- `DELETE /media/my-media` - Xóa nhiều media của người dùng
- `POST /media/presigned-urls` - Tạo presigned URLs cho upload

### URL Management

#### User URL
- `GET /data/url` - Lấy danh sách URL với tìm kiếm và lọc
- `POST /data/url` - Tạo URL mới
- `GET /data/url/{id}` - Lấy chi tiết URL
- `PUT /data/url/{id}` - Cập nhật URL
- `DELETE /data/url/{id}` - Xóa URL

### Knowledge Files Management

#### Admin Knowledge Files
- `GET /admin/knowledge-files` - Lấy danh sách file tri thức
- `POST /admin/knowledge-files` - Tạo nhiều file tri thức
- `DELETE /admin/knowledge-files` - Xóa nhiều file tri thức

#### Admin Vector Store
- `GET /admin/vector-stores` - Lấy danh sách vector store

### Statistics

#### Admin Statistics
- `GET /admin/statistics` - Thống kê tổng quan hệ thống
- `GET /admin/statistics/storage` - Thống kê dung lượng lưu trữ

#### User Statistics
- `GET /statistics` - Thống kê dữ liệu người dùng
- `GET /statistics/storage` - Thống kê dung lượng người dùng

## 📋 Schemas chính

### Media DTOs
- `AdminMediaResponseDto` - Response cho admin media
- `MediaResponseDto` - Response cho user media
- `MediaDto` - DTO cơ bản cho media
- `DeleteMediaDto` - DTO xóa nhiều media

### URL DTOs
- `UrlSchema` - Schema cơ bản cho URL
- `CreateUrlDto` - DTO tạo URL mới
- `UpdateUrlDto` - DTO cập nhật URL

### Knowledge Files DTOs
- `FileResponseDto` - Response cho file tri thức
- `BatchCreateFilesDto` - DTO tạo nhiều file
- `BatchCreateFilesResponseDto` - Response tạo nhiều file
- `DeleteFilesDto` - DTO xóa nhiều file
- `VectorStoreResponseDto` - Response cho vector store

### Statistics DTOs
- `StatisticsResponseDto` - Thống kê tổng quan
- `StorageStatisticsResponseDto` - Thống kê dung lượng admin
- `UserStatisticsResponseDto` - Thống kê người dùng
- `UserStorageStatisticsResponseDto` - Thống kê dung lượng người dùng

## 🔧 Cách sử dụng

### 1. Import vào Swagger UI
```bash
# Copy nội dung file swagger.json và paste vào Swagger Editor
# Hoặc host file và truy cập qua URL
```

### 2. Sử dụng với Postman
```bash
# Import file swagger.json vào Postman Collection
# Tất cả endpoints sẽ được tự động tạo với examples
```

### 3. Generate Client Code
```bash
# Sử dụng OpenAPI Generator để tạo client code
npx @openapitools/openapi-generator-cli generate \
  -i src/modules/data/swagger.json \
  -g typescript-axios \
  -o ./generated-client
```

## 🎨 Features

### ✅ Hoàn chỉnh
- **OpenAPI 3.0.0** compliant
- **Comprehensive schemas** với validation rules
- **Detailed examples** cho tất cả request/response
- **Error handling** với các mã lỗi cụ thể
- **Security schemes** với JWT Bearer Auth
- **Pagination support** với PaginationMeta
- **Vietnamese descriptions** cho user-friendly

### ✅ Best Practices
- **Consistent naming** theo chuẩn dự án
- **Proper HTTP status codes** cho từng endpoint
- **Detailed parameter descriptions** với examples
- **Multiple request examples** cho better DX
- **Comprehensive error responses** cho debugging
- **Type safety** với proper schema definitions

## 🚀 Tích hợp

File swagger.json này có thể được tích hợp vào:

1. **API Gateway** - Để validation và documentation
2. **Frontend Applications** - Generate TypeScript types
3. **Testing Tools** - Automated API testing
4. **Documentation Sites** - Swagger UI, Redoc
5. **Client SDKs** - Generate client libraries

## 📝 Lưu ý

- File này chỉ bao gồm **Data Module APIs**
- Để có tài liệu đầy đủ, cần kết hợp với swagger của các module khác
- Tất cả endpoints đều yêu cầu authentication
- Response format tuân theo chuẩn `ApiResponseDto` của dự án
- Error codes tuân theo range đã định nghĩa cho từng module

## 🔄 Cập nhật

Khi có thay đổi API trong Data Module:

1. Cập nhật file `swagger.json` tương ứng
2. Kiểm tra tính hợp lệ với OpenAPI validator
3. Update documentation và examples
4. Regenerate client code nếu cần
5. Thông báo cho team về breaking changes

---

**Tạo bởi:** RedAI Development Team  
**Phiên bản:** 1.0.0  
**Cập nhật:** $(date)
