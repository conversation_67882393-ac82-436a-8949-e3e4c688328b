import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import { Url } from '../../entities/url.entity';
import { CrawlSession } from '../../entities/crawl-session.entity';
import { UrlRepository } from '../../repositories/url.repository';
import { CrawlSessionRepository } from '../../repositories/crawl-session.repository';
import { CreateUrlDto } from '../../schemas/create-url.dto';
import { UpdateUrlDto } from '../../schemas/update-url.dto';
import { PaginatedResult } from '@common/response/api-response-dto';
import { SortDirection } from '@common/dto/query.dto';
import { FindAllUrlDto } from '../dto/find-all-url.dto';
import { CrawlDto } from '../dto/crawl.dto';
import { OwnerType } from '../../constants/owner-type.enum';
import { StartCrawlWithTrackingDto, CrawlProgressResponseDto, CrawlSessionListResponseDto, StartCrawlResponseDto } from '../dto/crawl-progress.dto';
import { URL_ERROR_CODES } from 'src/modules/data/url/exceptions';
import { AppException } from '@/common';
import { QueueService } from '@shared/queue';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bull';
import { QueueName } from '@shared/queue/queue.constants';

@Injectable()
export class UrlUserService {
  private readonly logger = new Logger(UrlUserService.name);

  constructor(
    @InjectRepository(Url)
    private readonly urlRepository: Repository<Url>,
    @InjectRepository(CrawlSession)
    private readonly crawlSessionRepository: Repository<CrawlSession>,
    private readonly urlCustomRepository: UrlRepository,
    private readonly crawlSessionCustomRepository: CrawlSessionRepository,
    private readonly queueService: QueueService,
    @InjectQueue(QueueName.CRAWL_URL)
    private readonly crawlQueue: Queue,
  ) {}


  /**
   * Tìm URL theo ID và kiểm tra quyền truy cập
   * @param userId ID của người dùng
   * @param id ID của URL
   * @returns URL nếu tìm thấy và có quyền truy cập
   */
  async findUrlById(userId: number, id: string): Promise<Url> {
    try {
      const url = await this.urlRepository.findOne({
        where: { id },
      });

      if (!url) {
        throw new AppException(
          URL_ERROR_CODES.URL_NOT_FOUND,
          'Không tìm thấy URL',
        );
      }

      // Kiểm tra quyền truy cập
      if (url.ownedBy !== userId) {
        throw new AppException(
          URL_ERROR_CODES.URL_ACCESS_DENIED,
          'Bạn không có quyền truy cập URL này',
        );
      }

      return url;
    } catch (error) {
      // Nếu lỗi đã được xử lý, ném lại
      if (error instanceof AppException) {
        throw error;
      }

      // Xử lý các lỗi khác
      throw new AppException(
        URL_ERROR_CODES.URL_SEARCH_FAILED,
        `Không thể tìm URL: ${error.message}`,
      );
    }
  }

  /**
   * Tìm tất cả URL của người dùng với phân trang và tìm kiếm
   * @param userId ID của người dùng
   * @param findAllUrlDto Thông tin tìm kiếm và phân trang
   * @returns Danh sách URL với phân trang
   */
  async findUrlsByOwner(
    userId: number,
    findAllUrlDto: FindAllUrlDto,
  ): Promise<PaginatedResult<Url>> {
    try {
      const { page = 1, limit = 10, search, sortBy = 'createdAt', sortDirection = SortDirection.DESC } = findAllUrlDto;

      // Tính toán offset
      const offset = (page - 1) * limit;

      // Tạo query builder
      const queryBuilder = this.urlRepository
        .createQueryBuilder('url')
        .where('url.ownedBy = :userId', { userId });

      // Thêm điều kiện tìm kiếm nếu có
      if (search && search.trim() !== '') {
        queryBuilder.andWhere(
          '(url.title ILIKE :search OR url.content ILIKE :search OR url.url ILIKE :search)',
          { search: `%${search.trim()}%` }
        );
      }

      // Thêm sắp xếp
      queryBuilder.orderBy(`url.${sortBy}`, sortDirection);

      // Thêm phân trang
      queryBuilder.skip(offset).take(limit);

      // Thực hiện query
      const [urls, total] = await queryBuilder.getManyAndCount();

      // Tính toán thông tin phân trang
      const totalPages = Math.ceil(total / limit);
      const hasNextPage = page < totalPages;
      const hasPrevPage = page > 1;

      return {
        items: urls,
        meta: {
          totalItems: total,
          itemCount: urls.length,
          itemsPerPage: limit,
          totalPages,
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(`Error finding URLs for user ${userId}: ${error.message}`, error.stack);
      throw new AppException(
        URL_ERROR_CODES.URL_SEARCH_FAILED,
        `Không thể tìm danh sách URL: ${error.message}`,
      );
    }
  }

  /**
   * Tìm kiếm URL với từ khóa
   * @param userId ID của người dùng
   * @param searchQuery Từ khóa tìm kiếm
   * @param page Trang hiện tại
   * @param limit Số lượng kết quả mỗi trang
   * @returns Danh sách URL phù hợp
   */
  async searchUrls(
    userId: number,
    searchQuery: string,
    page = 1,
    limit = 10,
  ): Promise<PaginatedResult<Url>> {
    try {
      if (!searchQuery || searchQuery.trim() === '') {
        // Nếu không có từ khóa, trả về danh sách rỗng
        return {
          items: [],
          meta: {
            totalItems: 0,
            itemCount: 0,
            itemsPerPage: limit,
            totalPages: 0,
            currentPage: page,
          },
        };
      }

      const offset = (page - 1) * limit;

      const queryBuilder = this.urlRepository
        .createQueryBuilder('url')
        .where('url.ownedBy = :userId', { userId })
        .andWhere(
          '(url.title ILIKE :search OR url.content ILIKE :search OR url.url ILIKE :search)',
          { search: `%${searchQuery.trim()}%` }
        )
        .orderBy('url.createdAt', 'DESC')
        .skip(offset)
        .take(limit);

      const [urls, total] = await queryBuilder.getManyAndCount();

      const totalPages = Math.ceil(total / limit);

      return {
        items: urls,
        meta: {
          totalItems: total,
          itemCount: urls.length,
          itemsPerPage: limit,
          totalPages,
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(`Error searching URLs: ${error.message}`, error.stack);
      throw new AppException(
        URL_ERROR_CODES.URL_SEARCH_FAILED,
        `Không thể tìm kiếm URL: ${error.message}`,
      );
    }
  }

  /**
   * Tạo URL mới
   * @param userId ID của người dùng
   * @param createUrlDto Thông tin URL cần tạo
   * @returns URL đã tạo
   */
  async createUrl(userId: number, createUrlDto: CreateUrlDto): Promise<Url> {
    try {
      // Kiểm tra URL có hợp lệ không
      try {
        new URL(createUrlDto.url);
      } catch (error) {
        throw new AppException(
          URL_ERROR_CODES.URL_INVALID_FORMAT,
          'URL không đúng định dạng',
        );
      }

      // Kiểm tra URL đã tồn tại chưa
      const existingUrl = await this.urlRepository.findOne({
        where: {
          url: createUrlDto.url,
          ownedBy: userId,
        },
      });

      if (existingUrl) {
        throw new AppException(
          URL_ERROR_CODES.URL_ALREADY_EXISTS,
          'URL này đã tồn tại trong hệ thống của bạn',
        );
      }

      // Tạo URL mới với ownedByEnum = USER
      const newUrl = new Url();
      newUrl.url = createUrlDto.url;
      newUrl.title = createUrlDto.title || '';
      newUrl.content = createUrlDto.content || '';
      newUrl.ownedBy = userId;
      newUrl.ownedByEnum = OwnerType.USER;
      newUrl.createdAt = Date.now();
      newUrl.updatedAt = Date.now();

      // Lưu URL
      return this.urlRepository.save(newUrl);
    } catch (error) {
      // Nếu lỗi đã được xử lý, ném lại
      if (error instanceof AppException) {
        throw error;
      }

      // Xử lý các lỗi khác
      throw new AppException(
        URL_ERROR_CODES.URL_CREATION_FAILED,
        `Không thể tạo URL: ${error.message}`,
      );
    }
  }

  /**
   * Cập nhật URL
   * @param id ID của URL
   * @param userId ID của người dùng
   * @param updateUrlDto Thông tin cập nhật
   * @returns URL đã cập nhật
   */
  async updateUrl(
    id: string,
    userId: number,
    updateUrlDto: UpdateUrlDto,
  ): Promise<Url> {
    try {
      // Tìm URL theo ID và kiểm tra quyền truy cập
      const url = await this.findUrlById(userId, id);

      // Kiểm tra URL có hợp lệ không (nếu có cập nhật URL)
      if (updateUrlDto.url) {
        try {
          new URL(updateUrlDto.url);
        } catch (error) {
          throw new AppException(
            URL_ERROR_CODES.URL_INVALID_FORMAT,
            'URL không đúng định dạng',
          );
        }

        // Kiểm tra URL mới đã tồn tại chưa (trừ URL hiện tại)
        const existingUrl = await this.urlRepository.findOne({
          where: {
            url: updateUrlDto.url,
          },
        });

        if (existingUrl && existingUrl.id !== id) {
          throw new AppException(
            URL_ERROR_CODES.URL_ALREADY_EXISTS,
            'URL này đã tồn tại trong hệ thống',
          );
        }
      }

      // Cập nhật thông tin URL
      Object.assign(url, updateUrlDto);
      url.updatedAt = Date.now();

      // Lưu URL đã cập nhật
      return this.urlRepository.save(url);
    } catch (error) {
      // Nếu lỗi đã được xử lý, ném lại
      if (error instanceof AppException) {
        throw error;
      }

      // Xử lý các lỗi khác
      throw new AppException(
        URL_ERROR_CODES.URL_UPDATE_FAILED,
        `Không thể cập nhật URL: ${error.message}`,
      );
    }
  }

  /**
   * Xóa nhiều URL cùng lúc
   * @param ids Danh sách ID của các URL cần xóa
   * @param userId ID của người dùng
   */
  async deleteUrls(ids: string[], userId: number): Promise<void> {
    try {
      this.logger.log(`Deleting URLs with IDs: ${ids.join(', ')} for user: ${userId}`);

      if (!ids || ids.length === 0) {
        this.logger.error('URL IDs are required');
        throw new AppException(
          URL_ERROR_CODES.URL_INVALID_PARAMS,
          'Danh sách ID URL là bắt buộc',
        );
      }

      // Tìm tất cả URL theo IDs
      const urls = await this.urlRepository.find({
        where: { id: In(ids) },
      });

      if (urls.length === 0) {
        this.logger.warn(`No URLs found with IDs: ${ids.join(', ')}`);
        throw new AppException(
          URL_ERROR_CODES.URL_NOT_FOUND,
          'Không tìm thấy URL nào để xóa',
        );
      }

      // Kiểm tra quyền truy cập cho tất cả URL
      const unauthorizedUrls = urls.filter((url) => url.ownedBy !== userId);
      if (unauthorizedUrls.length > 0) {
        this.logger.warn(
          `User ${userId} does not have access to URLs: ${unauthorizedUrls.map((u) => u.id).join(', ')}`,
        );
        throw new AppException(
          URL_ERROR_CODES.URL_ACCESS_DENIED,
          'Bạn không có quyền xóa một số URL này',
        );
      }

      // Xóa tất cả URL
      await this.urlRepository.remove(urls);

      this.logger.log(
        `Successfully deleted ${urls.length} URLs for user: ${userId}`,
      );
    } catch (error) {
      // Nếu lỗi đã được xử lý, ném lại
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Error deleting URLs: ${error.message}`, error.stack);
      throw new AppException(
        URL_ERROR_CODES.URL_DELETE_FAILED,
        `Không thể xóa URL: ${error.message}`,
      );
    }
  }

  /**
   * Xóa URL (deprecated - sử dụng deleteUrls thay thế)
   * @param id ID của URL
   * @param userId ID của người dùng
   */
  async deleteUrl(id: string, userId: number): Promise<void> {
    return this.deleteUrls([id], userId);
  }

  // ==================== CRAWL TRACKING METHODS ====================

  /**
   * Bắt đầu crawl với tracking session
   * @param userId ID của người dùng
   * @param crawlDto Thông tin crawl
   * @returns Session ID và Job ID
   */
  async startCrawlWithTracking(userId: number, crawlDto: StartCrawlWithTrackingDto): Promise<StartCrawlResponseDto> {
    try {
      this.logger.log(`Bắt đầu crawl với tracking cho user ${userId}, URL: ${crawlDto.url}`);

      // Tạo session ID duy nhất
      const timestamp = Date.now();
      const sessionId = `crawl_${userId}_${timestamp}`;

      // Tạo CrawlSession
      const crawlSession = new CrawlSession();
      crawlSession.id = sessionId;
      crawlSession.userId = userId;
      crawlSession.url = crawlDto.url;
      crawlSession.status = 'running';
      crawlSession.config = {
        depth: crawlDto.depth,
        maxUrls: crawlDto.maxUrls || 0,
        ignoreRobotsTxt: crawlDto.ignoreRobotsTxt || false,
      };
      crawlSession.progress = {
        totalUrls: 0,
        processedUrls: 0,
        successfulUrls: 0,
        failedUrls: 0,
        currentDepth: 0,
        percentage: 0,
      };
      crawlSession.startTime = timestamp;
      crawlSession.metadata = {
        source: 'user',
      };

      // Lưu session vào database
      await this.crawlSessionRepository.save(crawlSession);

      // Enqueue job với sessionId
      const jobId = await this.queueService.addCrawlUrlJob({
        userId,
        crawlDto: {
          url: crawlDto.url,
          depth: crawlDto.depth,
          ignoreRobotsTxt: crawlDto.ignoreRobotsTxt,
          maxUrls: crawlDto.maxUrls,
        },
        sessionId, // Thêm sessionId vào job data
      });

      // Cập nhật jobId vào session
      await this.crawlSessionRepository.update(sessionId, { jobId });

      this.logger.log(`Đã tạo crawl session ${sessionId} và job ${jobId}`);

      // Debug: Kiểm tra job status sau 2 giây
      // setTimeout(async () => {
      //   try {
      //     // const jobStatus = await this.queueService.getJobStatus(QueueName.CRAWL_URL, jobId);
      //     // // this.logger.log(`🔍 Job ${jobId} status after 2s: ${JSON.stringify(jobStatus)}`);
      //   } catch (error) {
      //     this.logger.error(`❌ Cannot get job status: ${error.message}`);
      //   }
      // }, 2000);

      return {
        sessionId,
        jobId,
        message: 'Tiếp nhận yêu cầu crawl. Sử dụng sessionId để theo dõi tiến độ.',
      };
    } catch (error) {
      this.logger.error(`Lỗi khi bắt đầu crawl với tracking: ${error.message}`, error.stack);
      throw new AppException(
        URL_ERROR_CODES.URL_CRAWL_FAILED,
        `Không thể bắt đầu crawl: ${error.message}`,
      );
    }
  }

  /**
   * Lấy tiến độ crawl theo session ID
   * @param userId ID của người dùng
   * @param sessionId ID của session
   * @returns Thông tin tiến độ crawl
   */
  async getCrawlProgress(userId: number, sessionId: string): Promise<CrawlProgressResponseDto> {
    try {
      this.logger.debug(`Lấy tiến độ crawl cho session ${sessionId} của user ${userId}`);

      const session = await this.crawlSessionCustomRepository.findSessionById(sessionId);

      if (!session) {
        throw new AppException(
          URL_ERROR_CODES.URL_NOT_FOUND,
          'Không tìm thấy session crawl',
        );
      }

      // Kiểm tra quyền truy cập
      if (session.userId !== userId) {
        throw new AppException(
          URL_ERROR_CODES.URL_ACCESS_DENIED,
          'Bạn không có quyền truy cập session này',
        );
      }

      return {
        sessionId: session.id,
        url: session.url,
        status: session.status,
        progress: session.progress,
        config: session.config,
        result: session.result,
        errors: session.errors,
        startTime: session.startTime,
        endTime: session.endTime,
        createdAt: session.createdAt,
        updatedAt: session.updatedAt,
        metadata: session.metadata,
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Lỗi khi lấy tiến độ crawl: ${error.message}`, error.stack);
      throw new AppException(
        URL_ERROR_CODES.URL_FETCH_FAILED,
        `Không thể lấy tiến độ crawl: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách sessions crawl của user
   * @param userId ID của người dùng
   * @param page Trang hiện tại
   * @param limit Số lượng items mỗi trang
   * @param status Lọc theo trạng thái (optional)
   * @returns Danh sách sessions với phân trang
   */
  async getUserCrawlSessions(
    userId: number,
    page: number = 1,
    limit: number = 20,
    status?: string,
  ): Promise<CrawlSessionListResponseDto> {
    try {
      this.logger.debug(`Lấy danh sách sessions cho user ${userId}, page ${page}, limit ${limit}`);

      const { sessions, total } = await this.crawlSessionCustomRepository.findUserSessions(
        userId,
        page,
        limit,
        status,
      );

      const totalPages = Math.ceil(total / limit);

      const sessionItems = sessions.map(session => ({
        sessionId: session.id,
        url: session.url,
        status: session.status,
        percentage: session.progress?.percentage || 0,
        processedUrls: session.progress?.processedUrls || 0,
        totalUrls: session.progress?.totalUrls || 0,
        createdAt: session.createdAt,
        endTime: session.endTime,
        message: session.result?.message,
      }));

      return {
        sessions: sessionItems,
        total,
        page,
        limit,
        totalPages,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách sessions: ${error.message}`, error.stack);
      throw new AppException(
        URL_ERROR_CODES.URL_FETCH_FAILED,
        `Không thể lấy danh sách sessions: ${error.message}`,
      );
    }
  }

  /**
   * Hủy session crawl đang chạy
   * @param userId ID của người dùng
   * @param sessionId ID của session
   * @returns true nếu hủy thành công
   */
  async cancelCrawlSession(userId: number, sessionId: string): Promise<boolean> {
    try {
      this.logger.log(`Hủy crawl session ${sessionId} cho user ${userId}`);

      const session = await this.crawlSessionCustomRepository.findSessionById(sessionId);

      if (!session) {
        throw new AppException(
          URL_ERROR_CODES.URL_NOT_FOUND,
          'Không tìm thấy session crawl',
        );
      }

      // Kiểm tra quyền truy cập
      if (session.userId !== userId) {
        throw new AppException(
          URL_ERROR_CODES.URL_ACCESS_DENIED,
          'Bạn không có quyền hủy session này',
        );
      }

      // Chỉ có thể hủy session đang chạy
      if (session.status !== 'running') {
        throw new AppException(
          URL_ERROR_CODES.URL_INVALID_PARAMS,
          'Chỉ có thể hủy session đang chạy',
        );
      }

      // Hủy job trong Bull queue nếu có jobId
      let jobCancelled = false;
      if (session.jobId) {
        try {
          this.logger.log(`🛑 Đang hủy job ${session.jobId} trong Bull queue...`);

          // Tìm job trong queue
          const job = await this.crawlQueue.getJob(session.jobId);
          if (job) {
            const jobState = await job.getState();
            this.logger.log(`📊 Job ${session.jobId} state: ${jobState}`);

            if (jobState === 'waiting' || jobState === 'delayed') {
              // Job chưa chạy, có thể remove
              await job.remove();
              jobCancelled = true;
              this.logger.log(`✅ Đã remove job ${session.jobId} khỏi queue`);
            } else if (jobState === 'active') {
              // Job đang chạy, cập nhật session để worker biết cần dừng
              this.logger.log(`🛑 Job ${session.jobId} đang active, cập nhật session để báo hiệu dừng`);

              // Cập nhật session status thành cancelled ngay lập tức
              await this.crawlSessionCustomRepository.updateSessionResult(
                sessionId,
                'cancelled',
                {
                  urlsProcessed: session.progress?.processedUrls || 0,
                  urlsSaved: 0,
                  message: 'Session đã bị hủy bởi người dùng',
                },
                Date.now(),
              );

              jobCancelled = true;
              this.logger.log(`✅ Đã cập nhật session ${sessionId} thành cancelled để worker dừng`);
            } else {
              this.logger.warn(`⚠️ Job ${session.jobId} ở trạng thái ${jobState}, không thể hủy`);
            }
          } else {
            this.logger.warn(`⚠️ Không tìm thấy job ${session.jobId} trong queue`);
          }
        } catch (jobError) {
          this.logger.error(`❌ Lỗi khi hủy job ${session.jobId}: ${jobError.message}`);
        }
      }

      // ✅ Đếm số URLs thực tế đã lưu vào database cho user này
      let actualUrlsSaved = 0;
      try {
        // Đếm URLs đã lưu trong khoảng thời gian crawl
        const startTime = session.startTime || Date.now();
        const endTime = Date.now();
        actualUrlsSaved = await this.urlCustomRepository.countUrlsByUserAndTimeRange(
          userId,
          startTime,
          endTime
        );
        this.logger.log(`📊 Đã lưu thực tế ${actualUrlsSaved} URLs vào database cho session ${sessionId}`);
      } catch (countError) {
        this.logger.warn(`⚠️ Không thể đếm URLs đã lưu: ${countError.message}`);
        // Fallback: sử dụng giá trị từ result hiện tại nếu có
        actualUrlsSaved = session.result?.urlsSaved || 0;
      }

      // ✅ Chỉ cập nhật session nếu chưa được cập nhật (tránh ghi đè)
      let success = true;
      if ((session.status as string) !== 'cancelled') {
        // ✅ Sử dụng kết quả thực tế thay vì progress
        const cancelResult = {
          urlsProcessed: session.progress?.processedUrls || 0,
          urlsSaved: actualUrlsSaved, // ✅ Sử dụng số thực tế đã lưu
          message: jobCancelled
            ? `Session và job đã bị hủy bởi người dùng. Đã lưu ${actualUrlsSaved} URLs vào database.`
            : `Session đã bị hủy (job có thể đã hoàn thành). Đã lưu ${actualUrlsSaved} URLs vào database.`,
        };

        // Cập nhật trạng thái session
        success = await this.crawlSessionCustomRepository.updateSessionResult(
          sessionId,
          'cancelled',
          cancelResult,
          Date.now(),
        );
      } else {
        this.logger.log(`ℹ️ Session ${sessionId} đã ở trạng thái cancelled, không cần cập nhật lại`);
      }

      if (success) {
        this.logger.log(`✅ Đã hủy crawl session ${sessionId} (Job cancelled: ${jobCancelled})`);
      }

      return success;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Lỗi khi hủy crawl session: ${error.message}`, error.stack);
      throw new AppException(
        URL_ERROR_CODES.URL_PROCESSING_ERROR,
        `Không thể hủy session: ${error.message}`,
      );
    }
  }
}
