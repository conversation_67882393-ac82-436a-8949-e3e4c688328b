# Blog User API Documentation

## Tổng Quan

Tài liệu này mô tả chi tiết các API của module Blog dành cho người dùng để quản lý bài viết, bình luận và mua bài viết.

## Base URL
```
https://api.redai.com
```

## Authentication
Tất cả APIs yêu cầu JWT token của user trong header:
```
Authorization: Bearer <user_jwt_token>
```

---

## 1. Blog Management APIs

### GET /user/blogs
Lấy danh sách bài viết với các tùy chọn lọc nâng cao.

**Headers:**
- `Authorization: Bearer <token>` (required)

**Query Parameters:**
- `page` (integer, optional): Số trang, mặc định 1
- `limit` (integer, optional): Số lượng kết quả mỗi trang, mặc định 10, tối đa 100
- `status` (string, optional): <PERSON><PERSON><PERSON> theo trạng thái
  - Enum: `DRAFT`, `PENDING`, `APPROVED`, `REJECTED`
- `authorType` (string, optional): <PERSON><PERSON><PERSON> theo loại tác giả
  - Enum: `USER`, `SYSTEM`
- `search` (string, optional): Từ khóa tìm kiếm theo tiêu đề hoặc mô tả
- `sortBy` (string, optional): Trường sắp xếp
  - Enum: `createdAt`, `updatedAt`, `viewCount`, `like`, `point`
  - Mặc định: `createdAt`
- `sortDirection` (string, optional): Hướng sắp xếp
  - Enum: `ASC`, `DESC`
  - Mặc định: `DESC`
- `ownership_type` (string, optional): Lọc theo loại sở hữu
  - Enum: `CREATED`, `PURCHASED`, `NOT_OWNED`

**Response 200:**
```json
{
  "code": 200,
  "message": "Success",
  "result": {
    "content": [
      {
        "id": 1,
        "title": "Hướng dẫn lập trình JavaScript",
        "description": "Bài viết hướng dẫn cơ bản về JavaScript",
        "content": "https://cdn.example.com/blogs/content/123.html",
        "point": 100,
        "viewCount": 150,
        "thumbnailUrl": "https://cdn.example.com/blogs/thumbnails/123.jpg",
        "tags": ["javascript", "tutorial", "programming"],
        "createdAt": 1632474086123,
        "updatedAt": 1632474086123,
        "author": {
          "id": 1,
          "name": "Nguyễn Văn A",
          "type": "USER",
          "avatar": "https://cdn.example.com/avatars/user10.jpg"
        },
        "employeeModerator": null,
        "status": "APPROVED",
        "enable": true,
        "like": 45,
        "isPurchased": true
      }
    ],
    "totalItems": 100,
    "itemCount": 10,
    "itemsPerPage": 10,
    "totalPages": 10,
    "currentPage": 1
  }
}
```

**Error Responses:**
- `400`: Bad Request - Tham số không hợp lệ
- `401`: Unauthorized
- `500`: Internal Server Error

### GET /user/blogs/detail/{id}
Lấy thông tin chi tiết của một bài viết theo ID.

**Headers:**
- `Authorization: Bearer <token>` (required)

**Path Parameters:**
- `id` (integer, required): ID của bài viết

**Response 200:**
```json
{
  "code": 200,
  "message": "Success",
  "result": {
    "id": 1,
    "title": "Hướng dẫn lập trình JavaScript",
    "description": "Bài viết hướng dẫn cơ bản về JavaScript",
    "content": "https://cdn.example.com/blogs/content/123.html",
    "point": 100,
    "viewCount": 150,
    "thumbnailUrl": "https://cdn.example.com/blogs/thumbnails/123.jpg",
    "tags": ["javascript", "tutorial", "programming"],
    "createdAt": 1632474086123,
    "updatedAt": 1632474086123,
    "author": {
      "id": 1,
      "name": "Nguyễn Văn A",
      "type": "USER",
      "avatar": "https://cdn.example.com/avatars/user10.jpg"
    },
    "employeeModerator": null,
    "status": "APPROVED",
    "enable": true,
    "like": 45,
    "isPurchased": true
  }
}
```

**Error Responses:**
- `401`: Unauthorized
- `404`: Not Found - Không tìm thấy bài viết
- `500`: Internal Server Error

### POST /user/blogs
Tạo bài viết mới và trả về URL để upload nội dung và thumbnail.

**Headers:**
- `Authorization: Bearer <token>` (required)
- `Content-Type: application/json`

**Request Body:**
```json
{
  "title": "Hướng dẫn lập trình JavaScript",
  "description": "Bài viết hướng dẫn cơ bản về JavaScript",
  "contentMediaType": "text/html",
  "thumbnailMediaType": "image/jpeg",
  "point": 100,
  "tags": ["javascript", "tutorial"],
  "status": "DRAFT"
}
```

**Body Parameters:**
- `title` (string, required): Tiêu đề bài viết (tối đa 500 ký tự)
- `description` (string, required): Mô tả bài viết (tối đa 1000 ký tự)
- `contentMediaType` (string, required): Loại media của nội dung
- `thumbnailMediaType` (string, required): Loại media của thumbnail
- `point` (integer, required): Số point để mua bài viết (>= 0)
- `tags` (array, required): Tags của bài viết (mỗi tag tối đa 50 ký tự)
- `status` (string, optional): Trạng thái bài viết, mặc định DRAFT

**Response 201:**
```json
{
  "code": 201,
  "message": "Blog created successfully",
  "result": {
    "id": 1,
    "contentUploadUrl": "https://s3.amazonaws.com/bucket/upload-content-url",
    "thumbnailUploadUrl": "https://s3.amazonaws.com/bucket/upload-thumbnail-url"
  }
}
```

**Error Responses:**
- `400`: Bad Request - Dữ liệu không hợp lệ
- `401`: Unauthorized
- `500`: Internal Server Error

### PUT /user/blogs/{id}/media
Cập nhật nội dung hoặc thumbnail của bài viết và trả về URL để upload.

**Headers:**
- `Authorization: Bearer <token>` (required)
- `Content-Type: application/json`

**Path Parameters:**
- `id` (integer, required): ID của bài viết

**Request Body:**
```json
{
  "media_type": "content",
  "media_content_type": "text/html"
}
```

**Body Parameters:**
- `media_type` (string, required): Loại media cần cập nhật
  - Enum: `content`, `thumbnail`
- `media_content_type` (string, required): Loại nội dung media

**Response 200:**
```json
{
  "code": 200,
  "message": "Media updated successfully",
  "result": {
    "uploadUrl": "https://s3.amazonaws.com/bucket/upload-url"
  }
}
```

**Error Responses:**
- `400`: Bad Request - Dữ liệu không hợp lệ
- `401`: Unauthorized
- `403`: Forbidden - Không có quyền cập nhật bài viết này
- `404`: Not Found - Không tìm thấy bài viết
- `500`: Internal Server Error

### PUT /user/blogs/{id}/submit
Gửi bài viết từ trạng thái DRAFT sang PENDING để chờ kiểm duyệt.

**Headers:**
- `Authorization: Bearer <token>` (required)

**Path Parameters:**
- `id` (integer, required): ID của bài viết

**Response 200:**
```json
{
  "code": 200,
  "message": "Blog submitted for review successfully"
}
```

**Error Responses:**
- `400`: Bad Request - Bài viết không ở trạng thái DRAFT
- `401`: Unauthorized
- `403`: Forbidden - Không có quyền gửi bài viết này
- `404`: Not Found - Không tìm thấy bài viết
- `500`: Internal Server Error

### DELETE /user/blogs/{id}
Xóa bài viết (chỉ có thể xóa bài viết ở trạng thái DRAFT hoặc REJECTED).

**Headers:**
- `Authorization: Bearer <token>` (required)

**Path Parameters:**
- `id` (integer, required): ID của bài viết

**Response 200:**
```json
{
  "code": 200,
  "message": "Blog deleted successfully"
}
```

**Error Responses:**
- `400`: Bad Request - Bài viết không thể xóa ở trạng thái hiện tại
- `401`: Unauthorized
- `403`: Forbidden - Không có quyền xóa bài viết này
- `404`: Not Found - Không tìm thấy bài viết
- `500`: Internal Server Error

---

## 2. Blog Comments APIs

### POST /user/blogs/{blogId}/comments
Tạo bình luận mới cho bài viết.

**Headers:**
- `Authorization: Bearer <token>` (required)
- `Content-Type: application/json`

**Path Parameters:**
- `blogId` (integer, required): ID của bài viết

**Request Body:**
```json
{
  "content": "Bài viết rất hay và bổ ích!",
  "parentCommentId": 1
}
```

**Body Parameters:**
- `content` (string, required): Nội dung bình luận (tối đa 1000 ký tự)
- `parentCommentId` (integer, optional): ID bình luận cha (để tạo reply)

**Response 201:**
```json
{
  "code": 201,
  "message": "Comment created successfully"
}
```

**Error Responses:**
- `400`: Bad Request - Dữ liệu không hợp lệ
- `401`: Unauthorized
- `404`: Not Found - Không tìm thấy bài viết
- `500`: Internal Server Error

### GET /user/blogs/{blogId}/comments
Lấy danh sách bình luận của bài viết với phân trang.

**Headers:**
- `Authorization: Bearer <token>` (required)

**Path Parameters:**
- `blogId` (integer, required): ID của bài viết

**Query Parameters:**
- `page` (integer, optional): Số trang, mặc định 1
- `limit` (integer, optional): Số lượng kết quả mỗi trang, mặc định 10, tối đa 100

**Response 200:**
```json
{
  "code": 200,
  "message": "Success",
  "result": {
    "content": [
      {
        "id": 1,
        "blogId": 1,
        "userId": 10,
        "employeeId": null,
        "content": "Bài viết rất hay và bổ ích!",
        "authorType": "USER",
        "parentCommentId": null,
        "createdAt": 1632474086123,
        "replies": [
          {
            "id": 2,
            "blogId": 1,
            "userId": 11,
            "employeeId": null,
            "content": "Cảm ơn bạn đã chia sẻ!",
            "authorType": "USER",
            "parentCommentId": 1,
            "createdAt": 1632474186123,
            "replies": []
          }
        ]
      }
    ],
    "totalItems": 50,
    "itemCount": 10,
    "itemsPerPage": 10,
    "totalPages": 5,
    "currentPage": 1
  }
}
```

**Error Responses:**
- `401`: Unauthorized
- `404`: Not Found - Không tìm thấy bài viết
- `500`: Internal Server Error

### DELETE /user/blogs/comments/{id}
Xóa bình luận theo ID (chỉ có thể xóa bình luận của chính mình).

**Headers:**
- `Authorization: Bearer <token>` (required)

**Path Parameters:**
- `id` (integer, required): ID của bình luận

**Response 200:**
```json
{
  "code": 200,
  "message": "Comment deleted successfully"
}
```

**Error Responses:**
- `401`: Unauthorized
- `403`: Forbidden - Không có quyền xóa bình luận này
- `404`: Not Found - Không tìm thấy bình luận
- `500`: Internal Server Error

---

## 3. Blog Purchase APIs

### POST /user/blogs/{blogId}/purchase
Mua bài viết bằng point.

**Headers:**
- `Authorization: Bearer <token>` (required)

**Path Parameters:**
- `blogId` (integer, required): ID của bài viết

**Response 201:**
```json
{
  "code": 201,
  "message": "Blog purchased successfully",
  "result": null
}
```

**Error Responses:**
- `400`: Bad Request - Không đủ point hoặc đã mua trước đó
- `401`: Unauthorized
- `404`: Not Found - Không tìm thấy bài viết
- `500`: Internal Server Error

### GET /user/blogs/{blogId}/purchased
Kiểm tra người dùng đã mua bài viết chưa.

**Headers:**
- `Authorization: Bearer <token>` (required)

**Path Parameters:**
- `blogId` (integer, required): ID của bài viết

**Response 200:**
```json
{
  "code": 200,
  "message": "Success",
  "result": {
    "purchased": true,
    "purchased_at": 1632474086123
  }
}
```

**Error Responses:**
- `401`: Unauthorized
- `404`: Not Found - Không tìm thấy bài viết
- `500`: Internal Server Error

### GET /user/blogs/purchases
Lấy danh sách bài viết đã mua với phân trang.

**Headers:**
- `Authorization: Bearer <token>` (required)

**Query Parameters:**
- `page` (integer, optional): Số trang, mặc định 1
- `limit` (integer, optional): Số lượng kết quả mỗi trang, mặc định 10, tối đa 100

**Response 200:**
```json
{
  "code": 200,
  "message": "Success",
  "result": {
    "content": [
      {
        "id": 1,
        "blogId": 1,
        "userId": 10,
        "point": 100,
        "purchased_at": 1632474086123,
        "blog": {
          "id": 1,
          "title": "Hướng dẫn lập trình JavaScript",
          "description": "Bài viết hướng dẫn cơ bản về JavaScript",
          "content": "https://cdn.example.com/blogs/content/123.html",
          "point": 100,
          "viewCount": 150,
          "thumbnailUrl": "https://cdn.example.com/blogs/thumbnails/123.jpg",
          "tags": ["javascript", "tutorial"],
          "createdAt": 1632474086123,
          "updatedAt": 1632474086123,
          "author": {
            "id": 1,
            "name": "Nguyễn Văn A",
            "type": "USER",
            "avatar": "https://cdn.example.com/avatars/user10.jpg"
          },
          "status": "APPROVED",
          "enable": true,
          "like": 45
        }
      }
    ],
    "totalItems": 25,
    "itemCount": 10,
    "itemsPerPage": 10,
    "totalPages": 3,
    "currentPage": 1
  }
}
```

**Error Responses:**
- `401`: Unauthorized
- `500`: Internal Server Error

### GET /user/blogs/purchases/{purchaseId}
Lấy thông tin chi tiết về giao dịch mua bài viết.

**Headers:**
- `Authorization: Bearer <token>` (required)

**Path Parameters:**
- `purchaseId` (integer, required): ID của giao dịch mua

**Response 200:**
```json
{
  "code": 200,
  "message": "Success",
  "result": {
    "id": 1,
    "point": 100,
    "purchased_at": 1632474086123,
    "blog": {
      "id": 1,
      "title": "Hướng dẫn lập trình JavaScript",
      "description": "Bài viết hướng dẫn cơ bản về JavaScript",
      "content": "https://cdn.example.com/blogs/content/123.html",
      "point": 100,
      "viewCount": 150,
      "thumbnailUrl": "https://cdn.example.com/blogs/thumbnails/123.jpg",
      "tags": ["javascript", "tutorial"],
      "createdAt": 1632474086123,
      "updatedAt": 1632474086123,
      "author": {
        "id": 1,
        "name": "Nguyễn Văn A",
        "type": "USER",
        "avatar": "https://cdn.example.com/avatars/user10.jpg"
      },
      "status": "APPROVED",
      "enable": true,
      "like": 45
    },
    "buyer": {
      "id": 10,
      "name": "Nguyễn Văn B",
      "avatar": "https://cdn.example.com/avatars/user20.jpg"
    }
  }
}
```

**Error Responses:**
- `401`: Unauthorized
- `403`: Forbidden - Không có quyền xem giao dịch này
- `404`: Not Found - Không tìm thấy giao dịch
- `500`: Internal Server Error

---

## Error Codes

### Blog User Error Codes (20001-20099)

#### Blog Errors (20001-20009)
- `20001`: Không tìm thấy bài viết hoặc bài viết không khả dụng
- `20002`: Bạn không có quyền truy cập bài viết này
- `20003`: Trạng thái bài viết không hợp lệ cho thao tác này
- `20004`: Bài viết đã được gửi để kiểm duyệt
- `20005`: Chỉ bài viết ở trạng thái nháp mới có thể gửi kiểm duyệt
- `20006`: Lỗi khi tạo bài viết
- `20007`: Lỗi khi cập nhật bài viết
- `20008`: Lỗi khi xóa bài viết
- `20009`: Bài viết không thể xóa ở trạng thái hiện tại

#### Blog Comment Errors (20010-20019)
- `20010`: Không tìm thấy bình luận
- `20011`: Bạn không có quyền xóa bình luận này
- `20012`: Lỗi khi tạo bình luận
- `20013`: Lỗi khi xóa bình luận

#### Blog Purchase Errors (20020-20029)
- `20020`: Bài viết đã được mua trước đó
- `20021`: Không đủ point để mua bài viết
- `20022`: Không thể mua bài viết của chính mình
- `20023`: Bài viết chưa được phê duyệt
- `20024`: Lỗi khi xử lý giao dịch mua
- `20025`: Không tìm thấy giao dịch mua

---

## Validation Rules

### Blog Creation & Update
- Tiêu đề: Bắt buộc, tối đa 500 ký tự
- Mô tả: Bắt buộc, tối đa 1000 ký tự
- Point: Bắt buộc, >= 0
- Tags: Bắt buộc, mỗi tag tối đa 50 ký tự
- Content Media Type: Bắt buộc
- Thumbnail Media Type: Bắt buộc

### Blog Comments
- Nội dung: Bắt buộc, tối đa 1000 ký tự
- Parent Comment ID: Tùy chọn (để tạo reply)

### Phân Trang
- Page: >= 1
- Limit: 1-100
- Sort direction: ASC hoặc DESC

---

## Business Rules

### Blog Access Control
1. **Content Visibility**: Trường content chỉ trả về khi:
   - Người dùng là tác giả của bài viết, HOẶC
   - Người dùng đã mua bài viết
2. **Ownership Types**:
   - `CREATED`: Bài viết do người dùng tạo (tất cả trạng thái)
   - `PURCHASED`: Bài viết đã mua (chỉ APPROVED)
   - `NOT_OWNED`: Bài viết chưa sở hữu (chỉ APPROVED)

### Blog Status Workflow
1. **DRAFT** → **PENDING**: Gửi để kiểm duyệt
2. **PENDING** → **APPROVED/REJECTED**: Admin kiểm duyệt
3. **REJECTED** → **PENDING**: Có thể gửi lại
4. **Delete**: Chỉ DRAFT hoặc REJECTED

### Purchase Rules
1. Không thể mua bài viết của chính mình
2. Không thể mua bài viết đã mua
3. Chỉ mua bài viết APPROVED
4. Phải có đủ point

---

## Rate Limiting

- **Blog Creation**: Tối đa 10 bài viết/ngày
- **Comment Creation**: Tối đa 50 bình luận/giờ
- **Purchase**: Tối đa 20 giao dịch/giờ
- **APIs khác**: Tối đa 1000 yêu cầu/giờ
