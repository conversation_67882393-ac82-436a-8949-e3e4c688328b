import { Body, Controller, Delete, Get, Param, ParseIntPipe, Post, Put, Query, UseGuards, Request } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse as ApiResponseDoc, ApiParam, ApiQuery, ApiBody, ApiBearerAuth, ApiExtraModels } from '@nestjs/swagger';
import { BlogResponseDto, PaginatedBlogResponseDto, CreateBlogDto, UpdateBlogMediaDto, GetBlogsExtendedDto, GetBlogsUnifiedDto } from '../../dto';
import { SWAGGER_API_TAGS } from '@/common/swagger/swagger.tags';
import { BlogUserService } from '@modules/blog/user/services';
import { JwtUserGuard } from '@/modules/auth/guards';
import { ApiResponseDto } from '@/common/response';
import { BlogOwnershipEnum } from '../../enums';
import { CurrentUser } from '@/modules/auth/decorators';


@ApiTags(SWAGGER_API_TAGS.BLOGS)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtUserGuard)
@Controller('user/blogs')
@ApiExtraModels(GetBlogsExtendedDto, GetBlogsUnifiedDto)
export class BlogUserController {
  constructor(private readonly blogUserService: BlogUserService) { }

  /**
   * Lấy danh sách bài viết với các tùy chọn lọc nâng cao
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách bài viết',
    description: 'Lấy danh sách bài viết với các tùy chọn lọc nâng cao (theo trạng thái, loại tác giả, tìm kiếm, loại sở hữu). Có thể lọc theo blog người dùng tạo (CREATED), blog người dùng đã mua (PURCHASED), hoặc blog chưa sở hữu (NOT_OWNED). Khi lọc theo ownership_type=CREATED, sẽ trả về tất cả blog của người dùng với mọi trạng thái. Khi lọc theo ownership_type=PURCHASED hoặc NOT_OWNED, chỉ lọc theo trạng thái APPROVED. Lưu ý: Trường content sẽ trả về null nếu người dùng chưa mua bài viết hoặc không phải tác giả của bài viết.',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Trang hiện tại',
    type: Number,
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Số lượng bản ghi trên mỗi trang',
    type: Number,
    example: 10,
  })
  @ApiQuery({
    name: 'status',
    required: false,
    description: 'Lọc theo trạng thái. Khi lọc theo ownership_type=CREATED, tham số status sẽ bị bỏ qua và trả về tất cả trạng thái. Khi lọc theo ownership_type=PURCHASED hoặc NOT_OWNED, chỉ lọc theo trạng thái APPROVED và không thể can thiệp bằng tham số status. Khi không chỉ định ownership_type, mặc định sẽ là APPROVED.',
    type: String,
    example: 'APPROVED',
  })
  @ApiQuery({
    name: 'authorType',
    required: false,
    description: 'Lọc theo loại tác giả (USER hoặc SYSTEM)',
    type: String,
    example: 'USER',
  })
  // Đã bỏ lọc theo tags
  @ApiQuery({
    name: 'search',
    required: false,
    description: 'Tìm kiếm theo tiêu đề và nội dung',
    type: String,
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    description: 'Sắp xếp theo trường',
    type: String,
    example: 'createdAt',
  })
  @ApiQuery({
    name: 'sortDirection',
    required: false,
    description: 'Thứ tự sắp xếp (ASC/DESC)',
    type: String,
    example: 'DESC',
  })
  @ApiQuery({
    name: 'ownership_type',
    required: false,
    description: 'Lọc theo loại sở hữu bài viết: CREATED (bài viết do người dùng tạo với mọi trạng thái), PURCHASED (bài viết người dùng đã mua, chỉ trạng thái APPROVED), NOT_OWNED (bài viết chưa sở hữu, chỉ trạng thái APPROVED). Khi sử dụng PURCHASED hoặc NOT_OWNED, chỉ lọc theo trạng thái APPROVED và không thể can thiệp bằng tham số status.',
    enum: BlogOwnershipEnum,
    example: BlogOwnershipEnum.CREATED,
  })
  @ApiResponseDoc({
    status: 200,
    description: 'Danh sách bài viết đã được lấy thành công.',
    type: PaginatedBlogResponseDto,
  })
  async getBlogs(
    @CurrentUser('id') userId: number,
    @Query('page') page: string = '1',
    @Query('limit') limit: string = '10',
    @Query('status') status?: string,
    @Query('authorType') authorType?: string,
    @Query('search') search?: string,
    @Query('sortBy') sortBy: string = 'createdAt',
    @Query('sortDirection') sortDirection: string = 'DESC',
    @Query('ownership_type') ownership_type?: string,
  ): Promise<ApiResponseDto<PaginatedBlogResponseDto>> {
    try {
      // Tạo DTO thủ công
      const dto: GetBlogsUnifiedDto = {
        page: parseInt(page),
        limit: parseInt(limit),
        status: status as any,
        authorType: authorType as any,
        search,
        sortBy,
        sortDirection: sortDirection as any,
        ownership_type: ownership_type as any,
      };

      const result = await this.blogUserService.getBlogs(userId, dto);
      return {
        code: 200,
        message: 'Success',
        result,
      };
    } catch (error) {
      console.error('Error in getBlogs controller:', error);
      throw error;
    }
  }

  /**
   * Lấy chi tiết bài viết theo ID
   */
  @Get('detail/:id')
  @ApiOperation({
    summary: 'Lấy chi tiết bài viết',
    description: 'Lấy thông tin chi tiết của một bài viết theo ID. Lưu ý: Trường content sẽ trả về null nếu người dùng chưa mua bài viết hoặc không phải tác giả của bài viết.',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của bài viết',
    type: Number,
    example: 1,
  })
  @ApiResponseDoc({
    status: 200,
    description: 'Thông tin bài viết đã được lấy thành công.',
    type: BlogResponseDto,
  })
  async findOne(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser('id') userId: number
  ): Promise<ApiResponseDto<BlogResponseDto>> {
    const result = await this.blogUserService.findOne(id, userId);
    return {
      code: 200,
      message: 'Success',
      result,
    };
  }

  /**
   * Tạo bài viết mới
   */
  @Post()
  @ApiOperation({
    summary: 'Tạo bài viết mới',
    description: 'Tạo bài viết mới và trả về URL để upload nội dung và thumbnail',
  })
  @ApiBody({ type: CreateBlogDto })
  @ApiResponseDoc({
    status: 201,
    description: 'Bài viết đã được tạo thành công.',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 201 },
        message: { type: 'string', example: 'Blog created successfully' },
        result: {
          type: 'object',
          properties: {
            contentUploadUrl: {
              type: 'string',
              example: 'https://cdn-storage.example.com/temp/uploads/123456789/content.html?signature=abc123...',
            },
            thumbnailUploadUrl: {
              type: 'string',
              example: 'https://cdn-storage.example.com/temp/uploads/123456789/thumbnail.jpg?signature=def456...',
            },
          },
        },
      },
    },
  })
  async create(
    @CurrentUser('id') userId: number,
    @Body() createBlogDto: CreateBlogDto,
  ): Promise<ApiResponseDto<any>> {
    try {
      // Sanitize input to remove any control characters
      const sanitizedDto = this.sanitizeCreateBlogDto(createBlogDto);

      const result = await this.blogUserService.create(userId, sanitizedDto);
      return {
        code: 201,
        message: 'Blog created successfully',
        result,
      };
    } catch (error) {
      // If there's an error, log it and rethrow
      console.error('Error creating blog:', error);
      throw error;
    }
  }

  /**
   * Sanitize the CreateBlogDto to remove any control characters
   * @param dto The DTO to sanitize
   * @returns Sanitized DTO
   */
  private sanitizeCreateBlogDto(dto: CreateBlogDto): CreateBlogDto {
    const sanitized = { ...dto };

    // Sanitize string fields
    if (sanitized.title) {
      sanitized.title = this.sanitizeString(sanitized.title);
    }

    if (sanitized.description) {
      sanitized.description = this.sanitizeString(sanitized.description);
    }

    if (sanitized.contentMediaType) {
      sanitized.contentMediaType = this.sanitizeString(sanitized.contentMediaType);
    }

    if (sanitized.thumbnailMediaType) {
      sanitized.thumbnailMediaType = this.sanitizeString(sanitized.thumbnailMediaType);
    }

    // Sanitize array of strings (tags)
    if (Array.isArray(sanitized.tags)) {
      sanitized.tags = sanitized.tags.map(tag => this.sanitizeString(tag));
    }

    return sanitized;
  }

  /**
   * Sanitize a string by removing control characters
   * @param str The string to sanitize
   * @returns Sanitized string
   */
  private sanitizeString(str: string): string {
    if (!str) return str;

    // Remove control characters (ASCII codes 0-31 and 127)
    // This regex matches any character with ASCII code 0-31 (control characters) or 127 (DEL)
    return str.replace(/[\x00-\x1F\x7F]/g, '');
  }

  /**
   * Cập nhật media cho bài viết
   */
  @Put(':id/media')
  @ApiOperation({
    summary: 'Cập nhật media cho bài viết',
    description: 'Cập nhật media (nội dung hoặc thumbnail) cho bài viết. Lưu ý: Nếu media_type là "content" thì media_content_type phải là "text/html". Nếu media_type là "thumbnail" thì media_content_type phải là một trong các định dạng hình ảnh: "image/jpeg", "image/png", "image/webp", "image/gif".',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của bài viết',
    type: Number,
    example: 1,
  })
  @ApiBody({ type: UpdateBlogMediaDto })
  @ApiResponseDoc({
    status: 200,
    description: 'URL đã được tạo thành công.',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: 'Media URLs generated successfully' },
        result: {
          type: 'object',
          properties: {
            uploadUrl: {
              type: 'string',
              example: 'https://cdn-storage.example.com/temp/uploads/123456789/content.html?signature=abc123...',
            },
          },
        },
      },
    },
  })
  async updateMedia(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser('id') userId: number,
    @Body() updateMediaDto: UpdateBlogMediaDto,
  ): Promise<ApiResponseDto<any>> {
    try {
      // Sanitize input to remove any control characters
      const sanitizedDto = this.sanitizeUpdateMediaDto(updateMediaDto);

      const result = await this.blogUserService.updateMedia(id, userId, sanitizedDto);
      return {
        code: 200,
        message: 'Media URLs generated successfully',
        result,
      };
    } catch (error) {
      console.error('Error updating blog media:', error);
      throw error;
    }
  }

  /**
   * Sanitize the UpdateBlogMediaDto to remove any control characters
   * @param dto The DTO to sanitize
   * @returns Sanitized DTO
   */
  private sanitizeUpdateMediaDto(dto: UpdateBlogMediaDto): UpdateBlogMediaDto {
    // Create a new instance of UpdateBlogMediaDto to ensure all properties are preserved
    const sanitized = new UpdateBlogMediaDto();

    // Copy properties from the input DTO
    sanitized.media_type = dto.media_type;

    // Sanitize string fields
    if (dto.media_content_type) {
      sanitized.media_content_type = this.sanitizeString(dto.media_content_type);
    } else {
      sanitized.media_content_type = dto.media_content_type;
    }

    return sanitized;
  }

  /**
   * Gửi bài viết để kiểm duyệt
   */
  @Put(':id/submit')
  @ApiOperation({
    summary: 'Gửi bài viết để kiểm duyệt',
    description: 'Chuyển trạng thái bài viết từ DRAFT sang PENDING để chờ kiểm duyệt',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của bài viết',
    type: Number,
    example: 1,
  })

  @ApiResponseDoc({
    status: 200,
    description: 'Bài viết đã được gửi để kiểm duyệt.',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: 'Blog submitted for review' },
      },
    },
  })
  async submitForReview(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<null>> {
    await this.blogUserService.submitForReview(id, userId);
    return {
      code: 200,
      message: 'Blog submitted for review',
      result: null,
    };
  }

  /**
   * Hủy gửi kiểm duyệt bài viết
   */
  @Put(':id/cancel-submit')
  @ApiOperation({
    summary: 'Hủy gửi kiểm duyệt bài viết',
    description: 'Chuyển trạng thái bài viết từ PENDING về DRAFT',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của bài viết',
    type: Number,
    example: 1,
  })

  @ApiResponseDoc({
    status: 200,
    description: 'Đã hủy gửi kiểm duyệt bài viết.',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: 'Blog review cancelled' },
      },
    },
  })
  async cancelSubmit(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<null>> {
    await this.blogUserService.cancelSubmit(id, userId);
    return {
      code: 200,
      message: 'Blog review cancelled',
      result: null,
    };
  }

  /**
   * Xóa bài viết
   */
  @Delete(':id')
  @ApiOperation({
    summary: 'Xóa bài viết',
    description: 'Xóa mềm bài viết bằng cách đặt enable = false',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của bài viết',
    type: Number,
    example: 1,
  })

  @ApiResponseDoc({
    status: 200,
    description: 'Bài viết đã được xóa thành công.',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: 'Blog deleted successfully' },
      },
    },
  })
  async delete(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<null>> {
    await this.blogUserService.delete(id, userId);
    return {
      code: 200,
      message: 'Blog deleted successfully',
      result: null,
    };
  }


}
