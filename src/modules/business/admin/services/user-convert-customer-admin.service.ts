import { Injectable, Logger } from '@nestjs/common';
import { UserConvertCustomerRepository, UserConvertRepository } from '@modules/business/repositories';
import { UserRepository } from '@modules/user/repositories/user.repository';
import {
  QueryUserConvertCustomerDto,
  UserConvertCustomerResponseDto,
  UserConvertCustomerDetailResponseDto,
  UserConvertResponseDto,
  ConvertUserToCustomerDto,
  ConvertUserToCustomerResponseDto
} from '../dto';
import { PaginatedResult } from '@common/response/api-response-dto';
import { AppException } from '@common/exceptions';
import { BUSINESS_ADMIN_ERROR_CODES } from '@modules/business/admin/exceptions';
import { UserConvertCustomer, UserConvert } from '@modules/business/entities';
import { ValidationHelper } from '../helpers/validation.helper';

/**
 * Service xử lý nghiệp vụ liên quan đến khách hàng chuyển đổi cho admin
 */
@Injectable()
export class UserConvertCustomerAdminService {
  private readonly logger = new Logger(UserConvertCustomerAdminService.name);

  constructor(
    private readonly userConvertCustomerRepository: UserConvertCustomerRepository,
    private readonly userConvertRepository: UserConvertRepository,
    private readonly userRepository: UserRepository,
    private readonly validationHelper: ValidationHelper,
  ) {}

  /**
   * Lấy danh sách khách hàng chuyển đổi với phân trang và lọc
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param queryDto DTO chứa các tham số truy vấn
   * @returns Danh sách khách hàng chuyển đổi phân trang
   */
  async getUserConvertCustomers(
    employeeId: number,
    queryDto: QueryUserConvertCustomerDto,
  ): Promise<PaginatedResult<UserConvertCustomerResponseDto>> {
    this.logger.log(
      `Nhân viên ${employeeId} đang lấy danh sách khách hàng chuyển đổi với query: ${JSON.stringify(queryDto)}`,
      `method: ${this.getUserConvertCustomers.name}`
    );

    try {
      // Gọi repository để lấy dữ liệu
      const result = await this.userConvertCustomerRepository.findUserConvertCustomers(queryDto);

      // Chuyển đổi từ entity sang DTO
      const customerDtos = result.items.map(customer => this.mapToUserConvertCustomerResponseDto(customer));

      this.logger.log(`Đã tìm thấy ${result.items.length} khách hàng chuyển đổi`);

      return {
        items: customerDtos,
        meta: result.meta
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách khách hàng chuyển đổi: ${error.message}`,
        error.stack,
        `method: ${this.getUserConvertCustomers.name}`
      );
      throw new AppException(
        BUSINESS_ADMIN_ERROR_CODES.USER_CONVERT_CUSTOMER_FETCH_ERROR,
        'Lỗi khi lấy danh sách khách hàng chuyển đổi',
      );
    }
  }

  /**
   * Lấy chi tiết khách hàng chuyển đổi theo ID
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param customerId ID của khách hàng chuyển đổi
   * @returns Chi tiết khách hàng chuyển đổi
   */
  async getUserConvertCustomerById(
    employeeId: number,
    customerId: number,
  ): Promise<UserConvertCustomerDetailResponseDto> {
    this.logger.log(
      `Nhân viên ${employeeId} đang lấy chi tiết khách hàng chuyển đổi với ID: ${customerId}`,
      `method: ${this.getUserConvertCustomerById.name}`
    );

    try {
      // Lấy thông tin khách hàng chuyển đổi
      const customer = await this.userConvertCustomerRepository.findUserConvertCustomerById(customerId);

      // Kiểm tra khách hàng có tồn tại không
      this.validationHelper.validateUserConvertCustomerExists(customer);

      // Lấy danh sách bản ghi chuyển đổi của khách hàng
      const converts = await this.userConvertRepository.find({
        where: { convertCustomerId: customerId },
        order: { createdAt: 'DESC' }
      });

      this.logger.log(`Đã tìm thấy ${converts.length} bản ghi chuyển đổi của khách hàng ID: ${customerId}`);

      // Chuyển đổi từ entity sang DTO
      // Sử dụng non-null assertion vì đã kiểm tra bằng validationHelper
      const customerDto = this.mapToUserConvertCustomerResponseDto(customer!);
      const convertDtos = converts.map(convert => this.mapToUserConvertResponseDto(convert));

      return {
        ...customerDto,
        converts: convertDtos
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi lấy chi tiết khách hàng chuyển đổi: ${error.message}`,
        error.stack,
        `method: ${this.getUserConvertCustomerById.name}`
      );
      throw new AppException(
        BUSINESS_ADMIN_ERROR_CODES.USER_CONVERT_CUSTOMER_FETCH_ERROR,
        'Lỗi khi lấy chi tiết khách hàng chuyển đổi',
      );
    }
  }

  /**
   * Chuyển đổi từ entity UserConvertCustomer sang DTO UserConvertCustomerResponseDto
   * @param customer Entity UserConvertCustomer
   * @returns DTO UserConvertCustomerResponseDto
   */
  private mapToUserConvertCustomerResponseDto(customer: UserConvertCustomer): UserConvertCustomerResponseDto {
    return {
      id: Number(customer.id), // Đảm bảo id được chuyển đổi sang kiểu number
      avatar: customer.avatar,
      name: customer.name,
      email: customer.email,
      phone: customer.phone,
      platform: customer.platform,
      timezone: customer.timezone,
      createdAt: customer.createdAt,
      updatedAt: customer.updatedAt,
      userId: customer.userId,
      agentId: customer.agentId,
      metadata: customer.metadata ? Object.entries(customer.metadata).map(([fieldName, fieldValue]) => ({
        fieldName,
        fieldValue: fieldValue as string | number | boolean | string[] | number[]
      })) : [],
      address: customer.address
    };
  }

  /**
   * Chuyển đổi user thành customer
   * @param employeeId ID của admin thực hiện
   * @param convertDto DTO chuyển đổi
   * @returns Thông tin customer và conversion đã tạo
   */
  async convertUserToCustomer(employeeId: number, convertDto: ConvertUserToCustomerDto): Promise<ConvertUserToCustomerResponseDto> {
    try {
      this.logger.log(`Admin ${employeeId} đang chuyển đổi user ${convertDto.userId} thành customer`);

      // Kiểm tra user có tồn tại không
      const user = await this.userRepository.findById(convertDto.userId);
      if (!user) {
        throw new AppException(
          BUSINESS_ADMIN_ERROR_CODES.USER_CONVERT_CUSTOMER_NOT_FOUND,
          `User với ID ${convertDto.userId} không tồn tại`
        );
      }

      // Kiểm tra user đã được convert thành customer chưa
      const existingCustomer = await this.userConvertCustomerRepository.findByUserId(convertDto.userId);
      if (existingCustomer) {
        throw new AppException(
          BUSINESS_ADMIN_ERROR_CODES.USER_CONVERT_CUSTOMER_FETCH_ERROR,
          `User ${convertDto.userId} đã được chuyển đổi thành customer với ID ${existingCustomer.id}`
        );
      }

      // Kiểm tra số điện thoại đã tồn tại chưa (nếu user có phone)
      if (user.phoneNumber) {
        const existingCustomerByPhone = await this.userConvertCustomerRepository.findByPhone(user.phoneNumber);
        if (existingCustomerByPhone) {
          throw new AppException(
            BUSINESS_ADMIN_ERROR_CODES.USER_CONVERT_CUSTOMER_FETCH_ERROR,
            `Số điện thoại ${user.phoneNumber} đã tồn tại trong hệ thống customer`
          );
        }
      }

      // Chuẩn bị metadata
      const metadata: Record<string, unknown> = {};
      if (convertDto.metadata && convertDto.metadata.length > 0) {
        convertDto.metadata.forEach(field => {
          metadata[field.fieldName] = field.fieldValue;
        });
      }

      // Chuẩn bị email data
      const emailData = user.email ? { primary: user.email } : undefined;

      // Tạo customer từ user data
      const customerData: Partial<UserConvertCustomer> = {
        name: user.fullName,
        phone: user.phoneNumber,
        email: emailData,
        avatar: user.avatar,
        platform: convertDto.platform || 'Admin',
        timezone: 'Asia/Ho_Chi_Minh',
        userId: convertDto.userId,
        agentId: convertDto.agentId,
        metadata,
        address: user.address,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      };

      // Tạo customer
      const customer = await this.userConvertCustomerRepository.createUserConvertCustomer(customerData);

      // Tạo bản ghi conversion
      const conversionData: Partial<UserConvert> = {
        convertCustomerId: customer.id,
        userId: convertDto.userId,
        conversionType: convertDto.conversionType || 'admin_manual',
        source: convertDto.source || 'admin_panel',
        notes: convertDto.notes || `Chuyển đổi thủ công bởi admin ${employeeId}`,
        content: {
          adminId: employeeId,
          originalUserData: {
            id: user.id,
            fullName: user.fullName,
            email: user.email,
            phoneNumber: user.phoneNumber
          },
          convertedAt: Date.now()
        },
        createdAt: Date.now(),
        updatedAt: Date.now(),
      };

      const conversion = await this.userConvertRepository.createUserConvert(conversionData);

      this.logger.log(`Đã chuyển đổi user ${convertDto.userId} thành customer ${customer.id} thành công`);

      return {
        customer: this.mapToUserConvertCustomerResponseDto(customer),
        conversion: this.mapToUserConvertResponseDto(conversion),
        originalUser: {
          id: user.id,
          fullName: user.fullName,
          email: user.email,
          phoneNumber: user.phoneNumber,
          avatar: user.avatar,
          address: user.address
        }
      };
    } catch (error) {
      this.logger.error(`Lỗi khi chuyển đổi user thành customer: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ADMIN_ERROR_CODES.GENERAL_ERROR,
        `Lỗi khi chuyển đổi user thành customer: ${error.message}`
      );
    }
  }

  /**
   * Chuyển đổi từ entity UserConvert sang DTO UserConvertResponseDto
   * @param convert Entity UserConvert
   * @returns DTO UserConvertResponseDto
   */
  private mapToUserConvertResponseDto(convert: UserConvert): UserConvertResponseDto {
    return {
      id: Number(convert.id),
      convertCustomerId: Number(convert.convertCustomerId),
      userId: typeof convert.userId === 'string' ? Number(convert.userId) : convert.userId,
      conversionType: convert.conversionType,
      source: convert.source,
      notes: convert.notes,
      content: convert.content,
      createdAt: typeof convert.createdAt === 'string' ? Number(convert.createdAt) : convert.createdAt,
      updatedAt: typeof convert.updatedAt === 'string' ? Number(convert.updatedAt) : convert.updatedAt,
    };
  }
}
