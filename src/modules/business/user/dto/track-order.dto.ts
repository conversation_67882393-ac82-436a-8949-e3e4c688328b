import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';

/**
 * DTO cho request tracking đơn hàng
 */
export class TrackOrderRequestDto {
  @ApiProperty({
    description: 'ID đơn hàng cần tracking',
    example: 123,
    type: Number
  })
  @IsNotEmpty()
  @IsNumber()
  orderId: number;
}

/**
 * DTO cho response tracking đơn hàng
 */
export class TrackOrderResponseDto {
  @ApiProperty({
    description: 'ID đơn hàng',
    example: 123
  })
  orderId: number;

  @ApiProperty({
    description: 'Mã vận đơn',
    example: 'GHN123456789'
  })
  trackingNumber: string;

  @ApiProperty({
    description: 'Đơn vị vận chuyển',
    enum: ['GHN', 'GHTK'],
    example: 'GHN'
  })
  carrier: string;

  @ApiProperty({
    description: 'Thông tin trạng thái chi tiết từ nhà vận chuyển',
    type: 'object',
    additionalProperties: true,
    example: {
      status: 'delivered',
      status_text: 'Đã giao hàng',
      created: '2024-01-15 10:30:00',
      modified: '2024-01-16 14:20:00'
    }
  })
  status: any;

  @ApiProperty({
    description: 'Thời gian cập nhật cuối cùng (timestamp)',
    example: 1705394400000
  })
  lastUpdated: number;

  @ApiProperty({
    description: 'Trạng thái vận chuyển trong hệ thống',
    example: 'delivered',
    required: false
  })
  @IsOptional()
  systemStatus?: string;

  @ApiProperty({
    description: 'Ghi chú bổ sung',
    example: 'Đã giao hàng thành công',
    required: false
  })
  @IsOptional()
  notes?: string;
}

/**
 * DTO cho query parameters tracking
 */
export class TrackOrderQueryDto {
  @ApiProperty({
    description: 'Có cập nhật trạng thái trong hệ thống hay không',
    example: true,
    required: false,
    default: true
  })
  @IsOptional()
  updateStatus?: boolean = true;

  @ApiProperty({
    description: 'Có lấy thông tin chi tiết hay không',
    example: true,
    required: false,
    default: true
  })
  @IsOptional()
  detailed?: boolean = true;
}
