import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, IsObject, IsUUID, Matches, MaxLength, MinLength, IsArray, ValidateNested, IsUrl } from 'class-validator';
import { Type } from 'class-transformer';
import { MetadataFieldDto } from './metadata-field.dto';

/**
 * DTO cho việc cập nhật thông tin khách hàng chuyển đổi
 */
export class UpdateUserConvertCustomerDto {
  /**
   * Tên khách hàng
   * @example "Nguyễn Văn A"
   */
  @ApiProperty({
    description: 'Tên khách hàng',
    example: 'Nguyễn Văn A',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Tên khách hàng phải là chuỗi' })
  @MinLength(2, { message: 'Tên khách hàng phải có ít nhất 2 ký tự' })
  @MaxLength(255, { message: 'Tên khách hàng không được vượt quá 255 ký tự' })
  name?: string;

  /**
   * Số điện thoại khách hàng (unique)
   * @example "0912345678"
   */
  @ApiProperty({
    description: 'Số điện thoại khách hàng (unique)',
    example: '0912345678',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Số điện thoại phải là chuỗi' })
  @Matches(/^[\+]?[1-9][\d]{0,15}$/, {
    message: 'Số điện thoại không hợp lệ, phải là số điện thoại quốc tế hợp lệ',
  })
  phone?: string;

  /**
   * Email khách hàng (dạng JSON)
   * @example { "primary": "<EMAIL>", "secondary": "<EMAIL>" }
   */
  @ApiProperty({
    description: 'Email khách hàng (dạng JSON)',
    example: { primary: '<EMAIL>', secondary: '<EMAIL>' },
    required: false,
  })
  @IsOptional()
  @IsObject({ message: 'Email phải là đối tượng JSON' })
  email?: Record<string, string>;

  /**
   * Tag/nhãn cho khách hàng
   * @example ["VIP", "Potential", "Hot Lead"]
   */
  @ApiProperty({
    description: 'Tag/nhãn cho khách hàng',
    example: ['VIP', 'Potential', 'Hot Lead'],
    required: false,
    type: [String],
  })
  @IsOptional()
  tags?: string[];

  /**
   * Thông tin file avatar để upload qua S3
   */
  @ApiProperty({
    description: 'Thông tin file avatar để upload qua S3',
    type: 'object',
    properties: {
      fileName: {
        type: 'string',
        description: 'Tên file avatar',
        example: 'avatar.jpg'
      },
      mimeType: {
        type: 'string',
        description: 'Loại MIME của file',
        example: 'image/jpeg',
        enum: ['image/jpeg', 'image/png', 'image/webp', 'image/gif']
      }
    },
  })
  @IsOptional()
  @IsObject({ message: 'Thông tin avatar phải là đối tượng' })
  avatarFile?: {
    fileName: string;
    mimeType: string;
  };

  /**
   * Nền tảng nguồn (Facebook, Web,...)
   * @example "Facebook"
   */
  @ApiProperty({
    description: 'Nền tảng nguồn (Facebook, Web,...)',
    example: 'Facebook',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Nền tảng phải là chuỗi' })
  @MaxLength(50, { message: 'Nền tảng không được vượt quá 50 ký tự' })
  platform?: string;

  /**
   * Múi giờ của khách hàng
   * @example "Asia/Ho_Chi_Minh"
   */
  @ApiProperty({
    description: 'Múi giờ của khách hàng',
    example: 'Asia/Ho_Chi_Minh',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Múi giờ phải là chuỗi' })
  @MaxLength(50, { message: 'Múi giờ không được vượt quá 50 ký tự' })
  timezone?: string;

  /**
   * ID agent hỗ trợ khách hàng
   * @example "550e8400-e29b-41d4-a716-************"
   */
  @ApiProperty({
    description: 'ID agent hỗ trợ khách hàng',
    example: '550e8400-e29b-41d4-a716-************',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'ID agent phải là chuỗi' })
  @IsUUID('4', { message: 'ID agent phải là UUID hợp lệ' })
  agentId?: string;

  /**
   * Metadata - Dữ liệu trường tùy chỉnh với configId và giá trị
   * @example [{ "configId": "customer_age", "value": "25" }, { "configId": "customer_interest", "value": ["sports", "music"] }]
   */
  @ApiProperty({
    description: 'Metadata - Dữ liệu trường tùy chỉnh với configId và giá trị',
    type: [MetadataFieldDto],
    required: false,
    example: [
      {
        configId: 'day_of_birth',
        value: '28/11/2003'
      },
      {
        configId: 'haianh',
        value: 'Thông tin bổ sung'
      },
      {
        configId: 'sâs',
        value: 'Nguyễn Văn A'
      }
    ],
  })
  @IsOptional()
  @IsArray({ message: 'Metadata phải là mảng' })
  @ValidateNested({ each: true })
  @Type(() => MetadataFieldDto)
  metadata?: MetadataFieldDto[];

  /**
   * Link Facebook của khách hàng
   * @example "https://facebook.com/user123"
   */
  @ApiProperty({
    description: 'Link Facebook của khách hàng',
    example: 'https://facebook.com/user123',
    required: false,
    maxLength: 500,
  })
  @IsOptional()
  @IsString({ message: 'Link Facebook phải là chuỗi' })
  @IsUrl({}, { message: 'Link Facebook phải là URL hợp lệ' })
  @MaxLength(500, { message: 'Link Facebook không được vượt quá 500 ký tự' })
  facebookLink?: string;

  /**
   * Link Twitter của khách hàng
   * @example "https://twitter.com/user123"
   */
  @ApiProperty({
    description: 'Link Twitter của khách hàng',
    example: 'https://twitter.com/user123',
    required: false,
    maxLength: 500,
  })
  @IsOptional()
  @IsString({ message: 'Link Twitter phải là chuỗi' })
  @IsUrl({}, { message: 'Link Twitter phải là URL hợp lệ' })
  @MaxLength(500, { message: 'Link Twitter không được vượt quá 500 ký tự' })
  twitterLink?: string;

  /**
   * Link LinkedIn của khách hàng
   * @example "https://linkedin.com/in/user123"
   */
  @ApiProperty({
    description: 'Link LinkedIn của khách hàng',
    example: 'https://linkedin.com/in/user123',
    required: false,
    maxLength: 500,
  })
  @IsOptional()
  @IsString({ message: 'Link LinkedIn phải là chuỗi' })
  @IsUrl({}, { message: 'Link LinkedIn phải là URL hợp lệ' })
  @MaxLength(500, { message: 'Link LinkedIn không được vượt quá 500 ký tự' })
  linkedinLink?: string;

  /**
   * Link Zalo của khách hàng
   * @example "https://zalo.me/user123"
   */
  @ApiProperty({
    description: 'Link Zalo của khách hàng',
    example: 'https://zalo.me/user123',
    required: false,
    maxLength: 500,
  })
  @IsOptional()
  @IsString({ message: 'Link Zalo phải là chuỗi' })
  @IsUrl({}, { message: 'Link Zalo phải là URL hợp lệ' })
  @MaxLength(500, { message: 'Link Zalo không được vượt quá 500 ký tự' })
  zaloLink?: string;

  /**
   * Link Website của khách hàng
   * @example "https://example.com"
   */
  @ApiProperty({
    description: 'Link Website của khách hàng',
    example: 'https://example.com',
    required: false,
    maxLength: 500,
  })
  @IsOptional()
  @IsString({ message: 'Link Website phải là chuỗi' })
  @IsUrl({}, { message: 'Link Website phải là URL hợp lệ' })
  @MaxLength(500, { message: 'Link Website không được vượt quá 500 ký tự' })
  websiteLink?: string;

  /**
   * Địa chỉ khách hàng
   * @example "123 Đường ABC, Quận 1, TP.HCM"
   */
  @ApiProperty({
    description: 'Địa chỉ khách hàng',
    example: '123 Đường ABC, Quận 1, TP.HCM',
    required: false,
    maxLength: 500,
    nullable: true,
  })
  @IsOptional()
  @IsString({ message: 'Địa chỉ phải là chuỗi' })
  @MaxLength(500, { message: 'Địa chỉ không được vượt quá 500 ký tự' })
  address?: string | null;
}
